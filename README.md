# toc-donut: Table of Contents Generator using Donut

A production-quality command-line tool that automatically extracts hierarchical section headings from PDF documents using the Hugging Face Donut (Document Understanding Transformer) model and generates structured Table of Contents in JSON and Markdown formats.

## Features

- 🔍 **Automatic TOC Extraction**: Extract hierarchical headings (Title, H1, H2, etc.) from PDF documents
- 🤖 **AI-Powered**: Uses Hugging Face Donut model for document understanding
- 📄 **Multiple Output Formats**: Generate TOC in JSON and Markdown formats
- 🖼️ **Image Processing**: Converts PDF pages to images for optimal model input
- 🛠️ **CLI Interface**: Easy-to-use command-line tool
- ⚡ **Production Ready**: Robust error handling and validation

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd toc-donut

# Install dependencies
pip install -r requirements.txt

# Install the package
pip install -e .
```

## Quick Start

```bash
# Generate TOC in JSON format
toc-donut input/document.pdf --output toc.json --format json

# Generate TOC in Markdown format
toc-donut input/document.pdf --output toc.md --format markdown

# Generate both formats
toc-donut input/document.pdf --output-dir ./output --format both
```

## Usage

```bash
toc-donut [OPTIONS] INPUT_PDF

Options:
  --output, -o TEXT       Output file path
  --output-dir, -d TEXT   Output directory (for multiple formats)
  --format, -f TEXT       Output format: json, markdown, or both [default: json]
  --verbose, -v           Enable verbose logging
  --help                  Show this message and exit
```

## Project Structure

```
toc-donut/
├── toc_donut/              # Main package
│   ├── __init__.py
│   ├── cli.py              # Command-line interface
│   ├── pdf_processor.py    # PDF to image conversion
│   ├── donut_model.py      # Donut model integration
│   ├── text_processor.py   # Text processing and hierarchy detection
│   ├── toc_generator.py    # TOC generation logic
│   └── utils.py            # Utility functions
├── tests/                  # Test suite
├── examples/               # Usage examples
├── input/                  # Sample input files
├── requirements.txt        # Dependencies
├── setup.py               # Package setup
└── README.md              # This file
```

## System Requirements

- **Python**: 3.8 or higher
- **Memory**: 4GB RAM minimum, 8GB+ recommended
- **Storage**: 2GB free space for model cache
- **GPU**: Optional but recommended for faster processing

### Dependencies

- **PyTorch**: Deep learning framework
- **Transformers**: Hugging Face model library
- **PIL/Pillow**: Image processing
- **pdf2image**: PDF to image conversion
- **poppler-utils**: System dependency for PDF processing
- **click**: Command-line interface
- **rich**: Enhanced terminal output
- **loguru**: Advanced logging

### System Dependencies

**Windows:**
- Download poppler from [poppler-windows](https://github.com/oschwartz10612/poppler-windows/releases)

**macOS:**
```bash
brew install poppler
```

**Ubuntu/Debian:**
```bash
sudo apt-get install poppler-utils
```

## Advanced Usage

### Programmatic API

```python
from toc_donut import PDFProcessor, DonutModel, TextProcessor, TOCGenerator

# Initialize components
pdf_processor = PDFProcessor(dpi=200)
donut_model = DonutModel()
text_processor = TextProcessor()
toc_generator = TOCGenerator()

# Process PDF
image_paths = pdf_processor.convert_pdf_to_images("document.pdf")
page_results = donut_model.process_document_pages(image_paths)
headings = text_processor.process_pages(page_results)

# Generate TOC
toc_json = toc_generator.generate_toc_json(headings)
toc_markdown = toc_generator.generate_toc_markdown(headings)
```

### Custom Model Configuration

```python
# Use different Donut model
donut_model = DonutModel(
    model_name="naver-clova-ix/donut-base-finetuned-cord-v2",
    device="cuda",  # or "cpu"
    cache_dir="/path/to/cache"
)

# Custom PDF processing
pdf_processor = PDFProcessor(
    dpi=300,  # Higher resolution
    max_width=2048,
    max_height=2048
)
```

### Batch Processing

```bash
# Process multiple files
for pdf in *.pdf; do
    toc-donut "$pdf" --output-dir ./output --format both
done
```

## Output Formats

### JSON Structure

```json
{
  "metadata": {
    "generated_at": "2025-08-03T17:30:00",
    "total_headings": 15,
    "max_level": 3
  },
  "table_of_contents": [
    {
      "text": "Introduction",
      "level": 1,
      "page": 1,
      "confidence": 0.95,
      "children": [
        {
          "text": "Background",
          "level": 2,
          "page": 1,
          "confidence": 0.87,
          "children": []
        }
      ]
    }
  ]
}
```

### Markdown Structure

```markdown
# Table of Contents

- Introduction (Page 1)
  - Background (Page 1)
  - Related Work (Page 2)
- Methodology (Page 3)
  - Data Collection (Page 3)
  - Analysis (Page 4)
- Results (Page 5)
```

## Performance Optimization

- **GPU Usage**: Automatically uses CUDA if available
- **Memory Management**: Efficient image processing and cleanup
- **Caching**: Models are cached after first download
- **Batch Processing**: Process multiple documents efficiently

## Troubleshooting

See [TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md) for common issues and solutions.

## Documentation

- [API Reference](docs/API_REFERENCE.md) - Detailed API documentation
- [Examples](examples/) - Usage examples and tutorials
- [Troubleshooting](docs/TROUBLESHOOTING.md) - Common issues and solutions

## Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=toc_donut --cov-report=html
```

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Acknowledgments

- [Hugging Face](https://huggingface.co/) for the Donut model and transformers library
- [NAVER CLOVA](https://github.com/clovaai/donut) for the original Donut research
- The open-source community for the supporting libraries

## Citation

If you use toc-donut in your research, please cite:

```bibtex
@software{toc_donut,
  title={toc-donut: Table of Contents Generator using Donut},
  author={Donut Project Team},
  year={2025},
  url={https://github.com/example/toc-donut}
}
```
