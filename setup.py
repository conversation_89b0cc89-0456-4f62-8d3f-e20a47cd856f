#!/usr/bin/env python3
"""Setup script for toc-donut package."""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="toc-donut",
    version="1.0.0",
    author="Donut Project Team",
    author_email="<EMAIL>",
    description="Table of Contents Generator using Hugging Face Donut model",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/example/toc-donut",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Text Processing :: Markup",
        "Topic :: Utilities",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "toc-donut=toc_donut.cli:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
    keywords="pdf, table-of-contents, donut, document-understanding, nlp, ai",
    project_urls={
        "Bug Reports": "https://github.com/example/toc-donut/issues",
        "Source": "https://github.com/example/toc-donut",
        "Documentation": "https://github.com/example/toc-donut#readme",
    },
)
