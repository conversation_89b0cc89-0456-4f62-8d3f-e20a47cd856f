"""
Utility functions for toc-donut package.
"""

import os
import re
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from loguru import logger


def validate_pdf_file(file_path: Union[str, Path]) -> bool:
    """
    Validate if a file is a valid PDF.
    
    Args:
        file_path: Path to the file to validate
        
    Returns:
        True if file is a valid PDF, False otherwise
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        return False
    
    if not file_path.suffix.lower() == '.pdf':
        return False
    
    # Check PDF magic number
    try:
        with open(file_path, 'rb') as f:
            header = f.read(4)
            return header == b'%PDF'
    except Exception:
        return False


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename by removing invalid characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename safe for filesystem
    """
    # Remove invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    
    # Remove leading/trailing underscores and dots
    sanitized = sanitized.strip('_.')
    
    # Ensure filename is not empty
    if not sanitized:
        sanitized = "untitled"
    
    return sanitized


def calculate_file_hash(file_path: Union[str, Path]) -> str:
    """
    Calculate SHA-256 hash of a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Hexadecimal hash string
    """
    hash_sha256 = hashlib.sha256()
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_sha256.update(chunk)
    
    return hash_sha256.hexdigest()


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def clean_text(text: str) -> str:
    """
    Clean and normalize text content.
    
    Args:
        text: Raw text to clean
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove control characters except newlines and tabs
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Strip leading/trailing whitespace
    text = text.strip()
    
    return text


def ensure_directory(directory: Union[str, Path]) -> Path:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        directory: Directory path
        
    Returns:
        Path object for the directory
    """
    directory = Path(directory)
    directory.mkdir(parents=True, exist_ok=True)
    return directory


def get_available_models() -> List[str]:
    """
    Get list of available Donut models from Hugging Face.
    
    Returns:
        List of model names
    """
    # Common Donut models available on Hugging Face
    return [
        "naver-clova-ix/donut-base",
        "naver-clova-ix/donut-base-finetuned-docvqa",
        "naver-clova-ix/donut-base-finetuned-cord-v2",
        "naver-clova-ix/donut-base-finetuned-rvlcdip",
        "naver-clova-ix/donut-base-finetuned-zhtrainticket",
    ]


def validate_model_name(model_name: str) -> bool:
    """
    Validate if a model name is in the expected format.
    
    Args:
        model_name: Model name to validate
        
    Returns:
        True if model name appears valid
    """
    # Check for basic Hugging Face model name format
    if '/' in model_name and len(model_name.split('/')) == 2:
        return True
    
    # Check if it's a local path
    if Path(model_name).exists():
        return True
    
    return False


def merge_confidence_scores(scores: List[float], method: str = "average") -> float:
    """
    Merge multiple confidence scores using specified method.
    
    Args:
        scores: List of confidence scores
        method: Merging method ("average", "min", "max", "weighted")
        
    Returns:
        Merged confidence score
    """
    if not scores:
        return 0.0
    
    if method == "average":
        return sum(scores) / len(scores)
    elif method == "min":
        return min(scores)
    elif method == "max":
        return max(scores)
    elif method == "weighted":
        # Weight higher scores more heavily
        weights = [score ** 2 for score in scores]
        weighted_sum = sum(score * weight for score, weight in zip(scores, weights))
        weight_sum = sum(weights)
        return weighted_sum / weight_sum if weight_sum > 0 else 0.0
    else:
        return sum(scores) / len(scores)  # Default to average


def detect_language(text: str) -> str:
    """
    Simple language detection based on character patterns.
    
    Args:
        text: Text to analyze
        
    Returns:
        Detected language code
    """
    if not text:
        return "unknown"
    
    # Simple heuristics for common languages
    if re.search(r'[а-яё]', text.lower()):
        return "ru"  # Russian
    elif re.search(r'[一-龯]', text):
        return "zh"  # Chinese
    elif re.search(r'[ひらがなカタカナ]', text):
        return "ja"  # Japanese
    elif re.search(r'[가-힣]', text):
        return "ko"  # Korean
    elif re.search(r'[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]', text.lower()):
        return "es"  # Spanish/French/etc
    else:
        return "en"  # Default to English


def create_backup_filename(original_path: Union[str, Path]) -> Path:
    """
    Create a backup filename by adding timestamp.
    
    Args:
        original_path: Original file path
        
    Returns:
        Backup file path
    """
    from datetime import datetime
    
    original_path = Path(original_path)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    backup_name = f"{original_path.stem}_backup_{timestamp}{original_path.suffix}"
    return original_path.parent / backup_name


def log_system_info():
    """Log system information for debugging."""
    import platform
    import torch
    
    logger.info(f"Python version: {platform.python_version()}")
    logger.info(f"Platform: {platform.platform()}")
    logger.info(f"PyTorch version: {torch.__version__}")
    logger.info(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        logger.info(f"CUDA device count: {torch.cuda.device_count()}")
        logger.info(f"Current CUDA device: {torch.cuda.current_device()}")
        logger.info(f"CUDA device name: {torch.cuda.get_device_name()}")


def estimate_processing_time(page_count: int, model_type: str = "base") -> float:
    """
    Estimate processing time based on page count and model type.
    
    Args:
        page_count: Number of pages to process
        model_type: Type of model being used
        
    Returns:
        Estimated time in seconds
    """
    # Base estimates (seconds per page)
    base_times = {
        "base": 3.0,
        "large": 5.0,
        "finetuned": 2.5
    }
    
    time_per_page = base_times.get(model_type, 3.0)
    
    # Add overhead for model loading and setup
    overhead = 30.0
    
    return (page_count * time_per_page) + overhead
