#!/usr/bin/env python3
"""
Create a test PDF with clear headings for testing the improved toc-donut system.
"""

from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

def create_test_pdf():
    """Create a test PDF with clear heading structure."""
    
    # Create the PDF
    doc = SimpleDocTemplate("input/test_document.pdf", pagesize=letter)
    styles = getSampleStyleSheet()
    
    # Create custom styles for headings
    heading1_style = ParagraphStyle(
        'CustomHeading1',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=12,
        spaceBefore=12
    )
    
    heading2_style = ParagraphStyle(
        'CustomHeading2', 
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=8,
        spaceBefore=8
    )
    
    # Build the document content
    story = []
    
    # Title
    story.append(Paragraph("MACHINE LEARNING RESEARCH PAPER", heading1_style))
    story.append(Spacer(1, 0.2*inch))
    
    # Abstract
    story.append(Paragraph("ABSTRACT", heading1_style))
    story.append(Paragraph("This paper presents a comprehensive study of machine learning algorithms and their applications in modern data science.", styles['Normal']))
    story.append(Spacer(1, 0.2*inch))
    
    # Introduction
    story.append(Paragraph("1. INTRODUCTION", heading1_style))
    story.append(Paragraph("Machine learning has revolutionized the way we process and analyze data. This section provides an overview of the field.", styles['Normal']))
    story.append(Spacer(1, 0.1*inch))
    
    # Subsections
    story.append(Paragraph("1.1 Background", heading2_style))
    story.append(Paragraph("The background of machine learning dates back to the 1950s when researchers first began exploring artificial intelligence.", styles['Normal']))
    story.append(Spacer(1, 0.1*inch))
    
    story.append(Paragraph("1.2 Motivation", heading2_style))
    story.append(Paragraph("The motivation for this research stems from the need to improve classification accuracy in complex datasets.", styles['Normal']))
    story.append(Spacer(1, 0.2*inch))
    
    # Methodology
    story.append(Paragraph("2. METHODOLOGY", heading1_style))
    story.append(Paragraph("This section describes the methodology used in our research.", styles['Normal']))
    story.append(Spacer(1, 0.1*inch))
    
    story.append(Paragraph("2.1 Data Collection", heading2_style))
    story.append(Paragraph("Data was collected from multiple sources including public datasets and proprietary databases.", styles['Normal']))
    story.append(Spacer(1, 0.1*inch))
    
    story.append(Paragraph("2.2 Algorithm Design", heading2_style))
    story.append(Paragraph("We designed a novel algorithm that combines deep learning with traditional statistical methods.", styles['Normal']))
    story.append(Spacer(1, 0.2*inch))
    
    # Results
    story.append(Paragraph("3. RESULTS", heading1_style))
    story.append(Paragraph("Our experiments demonstrate significant improvements over existing methods.", styles['Normal']))
    story.append(Spacer(1, 0.1*inch))
    
    story.append(Paragraph("3.1 Performance Metrics", heading2_style))
    story.append(Paragraph("We evaluated our approach using standard performance metrics including accuracy, precision, and recall.", styles['Normal']))
    story.append(Spacer(1, 0.1*inch))
    
    story.append(Paragraph("3.2 Comparison with Baselines", heading2_style))
    story.append(Paragraph("Comparison with baseline methods shows our approach achieves 15% better accuracy.", styles['Normal']))
    story.append(Spacer(1, 0.2*inch))
    
    # Conclusion
    story.append(Paragraph("4. CONCLUSION", heading1_style))
    story.append(Paragraph("In conclusion, our research demonstrates the effectiveness of the proposed approach.", styles['Normal']))
    story.append(Spacer(1, 0.2*inch))
    
    # References
    story.append(Paragraph("REFERENCES", heading1_style))
    story.append(Paragraph("[1] Smith, J. (2020). Machine Learning Fundamentals. Journal of AI Research.", styles['Normal']))
    story.append(Paragraph("[2] Johnson, A. (2021). Deep Learning Applications. Conference on Neural Networks.", styles['Normal']))
    
    # Build the PDF
    doc.build(story)
    print("✅ Test PDF created: input/test_document.pdf")

if __name__ == "__main__":
    try:
        create_test_pdf()
    except ImportError:
        print("❌ reportlab not installed. Installing...")
        import subprocess
        subprocess.run(["pip", "install", "reportlab"])
        create_test_pdf()
    except Exception as e:
        print(f"❌ Error creating PDF: {e}")
        print("Creating a simple text-based test instead...")
        
        # Create a simple text file that we can convert to PDF manually
        test_content = """
MACHINE LEARNING RESEARCH PAPER

ABSTRACT

This paper presents a comprehensive study of machine learning algorithms.

1. INTRODUCTION

Machine learning has revolutionized data processing.

1.1 Background

The background of machine learning dates back to the 1950s.

1.2 Motivation  

The motivation stems from improving classification accuracy.

2. METHODOLOGY

This section describes our research methodology.

2.1 Data Collection

Data was collected from multiple sources.

2.2 Algorithm Design

We designed a novel algorithm combining deep learning.

3. RESULTS

Our experiments demonstrate significant improvements.

3.1 Performance Metrics

We evaluated using standard performance metrics.

3.2 Comparison with Baselines

Comparison shows 15% better accuracy.

4. CONCLUSION

Our research demonstrates the effectiveness of the approach.

REFERENCES

[1] Smith, J. (2020). Machine Learning Fundamentals.
[2] Johnson, A. (2021). Deep Learning Applications.
"""
        
        with open("test_document.txt", "w") as f:
            f.write(test_content)
        print("✅ Test text file created: test_document.txt")
        print("💡 You can convert this to PDF using online tools or print to PDF")
