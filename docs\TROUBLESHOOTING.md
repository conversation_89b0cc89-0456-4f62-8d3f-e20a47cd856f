# Troubleshooting Guide

This guide helps you resolve common issues when using toc-donut.

## Installation Issues

### Problem: Package installation fails

**Symptoms:**
- `pip install` errors
- Missing dependencies
- Version conflicts

**Solutions:**

1. **Update pip and setuptools:**
   ```bash
   pip install --upgrade pip setuptools wheel
   ```

2. **Install in a virtual environment:**
   ```bash
   python -m venv toc_donut_env
   source toc_donut_env/bin/activate  # On Windows: toc_donut_env\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Install dependencies manually:**
   ```bash
   pip install torch transformers Pillow pdf2image click
   ```

### Problem: Poppler not found

**Symptoms:**
- `pdf2image` errors about poppler
- "Unable to get page count" errors

**Solutions:**

**Windows:**
1. Download poppler from: https://github.com/oschwartz10612/poppler-windows/releases
2. Extract and add to PATH, or specify path in code:
   ```python
   pdf_processor = PDFProcessor(poppler_path="C:/path/to/poppler/bin")
   ```

**macOS:**
```bash
brew install poppler
```

**Ubuntu/Debian:**
```bash
sudo apt-get install poppler-utils
```

---

## Model Loading Issues

### Problem: Model download fails

**Symptoms:**
- Network timeouts
- "Connection broken" errors
- Incomplete downloads

**Solutions:**

1. **Check internet connection and try again**

2. **Use a different model:**
   ```bash
   toc-donut input.pdf --model naver-clova-ix/donut-base
   ```

3. **Download model manually:**
   ```python
   from transformers import DonutProcessor, VisionEncoderDecoderModel
   
   # This will cache the model locally
   processor = DonutProcessor.from_pretrained("naver-clova-ix/donut-base-finetuned-docvqa")
   model = VisionEncoderDecoderModel.from_pretrained("naver-clova-ix/donut-base-finetuned-docvqa")
   ```

4. **Set cache directory:**
   ```python
   donut_model = DonutModel(cache_dir="/path/to/cache")
   ```

### Problem: CUDA out of memory

**Symptoms:**
- "CUDA out of memory" errors
- Model fails to load on GPU

**Solutions:**

1. **Force CPU usage:**
   ```python
   donut_model = DonutModel(device="cpu")
   ```

2. **Reduce image resolution:**
   ```python
   pdf_processor = PDFProcessor(dpi=150)  # Lower DPI
   ```

3. **Process fewer pages at once:**
   ```bash
   toc-donut input.pdf --page-range "1-5"
   ```

---

## PDF Processing Issues

### Problem: PDF conversion fails

**Symptoms:**
- "PDF file not found" errors
- "Invalid PDF" errors
- Empty image output

**Solutions:**

1. **Verify PDF file:**
   ```python
   from toc_donut.utils import validate_pdf_file
   if not validate_pdf_file("input.pdf"):
       print("Invalid PDF file")
   ```

2. **Check PDF permissions:**
   - Ensure PDF is not password-protected
   - Verify file is not corrupted

3. **Try different DPI:**
   ```bash
   toc-donut input.pdf --dpi 150
   ```

### Problem: Poor text extraction quality

**Symptoms:**
- Missing headings
- Incorrect text extraction
- Low confidence scores

**Solutions:**

1. **Increase image resolution:**
   ```bash
   toc-donut input.pdf --dpi 300
   ```

2. **Try different model:**
   ```bash
   toc-donut input.pdf --model naver-clova-ix/donut-base-finetuned-docvqa
   ```

3. **Process specific pages:**
   ```bash
   toc-donut input.pdf --page-range "1-10"
   ```

4. **Check document quality:**
   - Ensure PDF has clear, readable text
   - Avoid scanned documents with poor quality

---

## Output Issues

### Problem: No headings detected

**Symptoms:**
- Empty TOC output
- "No headings detected" message

**Solutions:**

1. **Check document structure:**
   - Verify document has clear headings
   - Ensure headings follow standard formatting

2. **Adjust confidence threshold:**
   ```python
   # In custom code, filter by lower confidence
   headings = [h for h in all_headings if h.confidence > 0.3]
   ```

3. **Use verbose mode for debugging:**
   ```bash
   toc-donut input.pdf --verbose
   ```

### Problem: Incorrect heading hierarchy

**Symptoms:**
- Wrong heading levels
- Missing nested structure

**Solutions:**

1. **Review document formatting:**
   - Check if headings use consistent formatting
   - Verify numbering schemes (1., 1.1, 1.1.1)

2. **Post-process results:**
   ```python
   # Custom level adjustment
   for heading in headings:
       if "Chapter" in heading.text:
           heading.level = 1
   ```

---

## Performance Issues

### Problem: Slow processing

**Symptoms:**
- Long processing times
- High memory usage

**Solutions:**

1. **Use GPU acceleration:**
   ```python
   donut_model = DonutModel(device="cuda")
   ```

2. **Reduce image size:**
   ```python
   pdf_processor = PDFProcessor(dpi=150, max_width=1024, max_height=1024)
   ```

3. **Process in batches:**
   ```bash
   # Process specific page ranges
   toc-donut input.pdf --page-range "1-10"
   toc-donut input.pdf --page-range "11-20"
   ```

4. **Enable cleanup:**
   ```bash
   toc-donut input.pdf --cleanup
   ```

---

## Common Error Messages

### "Model loading failed"
- **Cause:** Network issues, insufficient memory, or corrupted cache
- **Solution:** Clear cache, check internet, try CPU mode

### "PDF conversion failed"
- **Cause:** Invalid PDF, missing poppler, or file permissions
- **Solution:** Verify PDF, install poppler, check file access

### "No text extracted"
- **Cause:** Poor image quality, wrong model, or unsupported document type
- **Solution:** Increase DPI, try different model, verify document quality

### "Permission denied"
- **Cause:** File access restrictions or insufficient permissions
- **Solution:** Check file permissions, run with appropriate privileges

---

## Getting Help

If you continue to experience issues:

1. **Enable verbose logging:**
   ```bash
   toc-donut input.pdf --verbose
   ```

2. **Check system requirements:**
   - Python 3.8+
   - Sufficient RAM (4GB+ recommended)
   - Internet connection for model download

3. **Create a minimal test case:**
   ```python
   from toc_donut import PDFProcessor
   processor = PDFProcessor()
   info = processor.get_pdf_info("test.pdf")
   print(info)
   ```

4. **Report issues with:**
   - Error messages
   - System information
   - Sample PDF (if possible)
   - Steps to reproduce

---

## Performance Tips

1. **Use appropriate DPI:** 150-200 for most documents, 300+ for high-quality needs
2. **Process in chunks:** Use page ranges for large documents
3. **Cache models:** Models are cached after first download
4. **Use GPU:** Significantly faster than CPU processing
5. **Clean up:** Enable cleanup to free disk space
