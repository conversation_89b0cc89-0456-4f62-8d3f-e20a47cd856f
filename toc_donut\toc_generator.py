"""
Table of Contents Generation Module

This module generates structured table of contents in JSON and Markdown formats
from hierarchical heading data.
"""

import json
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from loguru import logger

from .text_processor import Heading


class TOCGenerator:
    """
    Generates table of contents in various formats from hierarchical headings.
    """
    
    def __init__(self):
        """Initialize the TOC generator."""
        logger.info("TOCGenerator initialized")
    
    def generate_toc_json(
        self, 
        headings: List[Heading], 
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate table of contents in JSON format.
        
        Args:
            headings: List of hierarchical headings
            metadata: Optional metadata about the document
            
        Returns:
            Dictionary representing the TOC in JSON format
        """
        logger.info(f"Generating JSON TOC from {len(headings)} headings")
        
        # Build hierarchical structure
        toc_structure = self._build_hierarchical_structure(headings)
        
        # Create the complete TOC document
        toc_json = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "generator": "toc-donut",
                "version": "1.0.0",
                "total_headings": len(headings),
                "max_level": max((h.level for h in headings), default=0),
                **(metadata or {})
            },
            "table_of_contents": toc_structure,
            "headings_flat": [
                {
                    "text": h.text,
                    "level": h.level,
                    "page": h.page_number,
                    "confidence": round(h.confidence, 3)
                }
                for h in headings
            ]
        }
        
        logger.success("JSON TOC generated successfully")
        return toc_json
    
    def generate_toc_markdown(
        self, 
        headings: List[Heading], 
        metadata: Optional[Dict[str, Any]] = None,
        include_page_numbers: bool = True,
        include_confidence: bool = False
    ) -> str:
        """
        Generate table of contents in Markdown format.
        
        Args:
            headings: List of hierarchical headings
            metadata: Optional metadata about the document
            include_page_numbers: Whether to include page numbers
            include_confidence: Whether to include confidence scores
            
        Returns:
            Markdown-formatted table of contents
        """
        logger.info(f"Generating Markdown TOC from {len(headings)} headings")
        
        lines = []
        
        # Add header
        lines.append("# Table of Contents")
        lines.append("")
        
        # Add metadata if provided
        if metadata:
            lines.append("## Document Information")
            lines.append("")
            for key, value in metadata.items():
                if key not in ["generated_at", "generator", "version"]:
                    lines.append(f"- **{key.replace('_', ' ').title()}**: {value}")
            lines.append("")
            lines.append(f"*Generated by toc-donut on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
            lines.append("")
            lines.append("---")
            lines.append("")
        
        # Generate TOC entries
        if not headings:
            lines.append("*No headings detected in the document.*")
        else:
            for heading in headings:
                # Create indentation based on level
                indent = "  " * (heading.level - 1)
                
                # Create the TOC entry
                entry_parts = [f"{indent}- {heading.text}"]
                
                # Add page number if requested
                if include_page_numbers:
                    entry_parts.append(f" (Page {heading.page_number})")
                
                # Add confidence if requested
                if include_confidence:
                    entry_parts.append(f" [Confidence: {heading.confidence:.2f}]")
                
                lines.append("".join(entry_parts))
        
        # Add footer
        lines.append("")
        lines.append("---")
        lines.append(f"*Total headings: {len(headings)}*")
        
        markdown_content = "\n".join(lines)
        logger.success("Markdown TOC generated successfully")
        return markdown_content
    
    def save_toc_json(
        self, 
        headings: List[Heading], 
        output_path: Path, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Generate and save TOC in JSON format to file.
        
        Args:
            headings: List of hierarchical headings
            output_path: Path to save the JSON file
            metadata: Optional metadata about the document
        """
        toc_json = self.generate_toc_json(headings, metadata)
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(toc_json, f, indent=2, ensure_ascii=False)
        
        logger.success(f"JSON TOC saved to {output_path}")
    
    def save_toc_markdown(
        self, 
        headings: List[Heading], 
        output_path: Path, 
        metadata: Optional[Dict[str, Any]] = None,
        include_page_numbers: bool = True,
        include_confidence: bool = False
    ) -> None:
        """
        Generate and save TOC in Markdown format to file.
        
        Args:
            headings: List of hierarchical headings
            output_path: Path to save the Markdown file
            metadata: Optional metadata about the document
            include_page_numbers: Whether to include page numbers
            include_confidence: Whether to include confidence scores
        """
        markdown_content = self.generate_toc_markdown(
            headings, metadata, include_page_numbers, include_confidence
        )
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        logger.success(f"Markdown TOC saved to {output_path}")
    
    def _build_hierarchical_structure(self, headings: List[Heading]) -> List[Dict[str, Any]]:
        """
        Build hierarchical structure from flat list of headings.
        
        Args:
            headings: Flat list of headings
            
        Returns:
            Hierarchical structure as nested dictionaries
        """
        if not headings:
            return []
        
        # Sort headings by page number and level
        sorted_headings = sorted(headings, key=lambda h: (h.page_number, h.level))
        
        root_items = []
        stack = []  # Stack to keep track of parent items at each level
        
        for heading in sorted_headings:
            # Create the heading item
            heading_item = {
                "text": heading.text,
                "level": heading.level,
                "page": heading.page_number,
                "confidence": round(heading.confidence, 3),
                "children": []
            }
            
            # Find the appropriate parent level
            while stack and stack[-1]["level"] >= heading.level:
                stack.pop()
            
            # Add to parent or root
            if stack:
                stack[-1]["children"].append(heading_item)
            else:
                root_items.append(heading_item)
            
            # Add current item to stack
            stack.append(heading_item)
        
        return root_items
    
    def generate_summary_stats(self, headings: List[Heading]) -> Dict[str, Any]:
        """
        Generate summary statistics about the TOC.
        
        Args:
            headings: List of headings
            
        Returns:
            Dictionary with summary statistics
        """
        if not headings:
            return {
                "total_headings": 0,
                "levels": {},
                "pages": {},
                "avg_confidence": 0.0
            }
        
        # Count headings by level
        level_counts = {}
        for heading in headings:
            level_counts[heading.level] = level_counts.get(heading.level, 0) + 1
        
        # Count headings by page
        page_counts = {}
        for heading in headings:
            page_counts[heading.page_number] = page_counts.get(heading.page_number, 0) + 1
        
        # Calculate average confidence
        avg_confidence = sum(h.confidence for h in headings) / len(headings)
        
        return {
            "total_headings": len(headings),
            "levels": level_counts,
            "pages": page_counts,
            "avg_confidence": round(avg_confidence, 3),
            "max_level": max(h.level for h in headings),
            "min_level": min(h.level for h in headings),
            "page_range": {
                "first": min(h.page_number for h in headings),
                "last": max(h.page_number for h in headings)
            }
        }
    
    def validate_toc_structure(self, headings: List[Heading]) -> Dict[str, Any]:
        """
        Validate the TOC structure and identify potential issues.
        
        Args:
            headings: List of headings to validate
            
        Returns:
            Dictionary with validation results
        """
        issues = []
        warnings = []
        
        if not headings:
            issues.append("No headings found in document")
            return {"valid": False, "issues": issues, "warnings": warnings}
        
        # Check for level gaps
        levels = sorted(set(h.level for h in headings))
        for i in range(len(levels) - 1):
            if levels[i + 1] - levels[i] > 1:
                warnings.append(f"Level gap detected: Level {levels[i]} to {levels[i + 1]}")
        
        # Check for very low confidence headings
        low_confidence = [h for h in headings if h.confidence < 0.5]
        if low_confidence:
            warnings.append(f"{len(low_confidence)} headings have low confidence (<0.5)")
        
        # Check for very short headings
        short_headings = [h for h in headings if len(h.text.strip()) < 3]
        if short_headings:
            warnings.append(f"{len(short_headings)} headings are very short (<3 characters)")
        
        # Check for duplicate headings
        texts = [h.text.lower().strip() for h in headings]
        duplicates = len(texts) - len(set(texts))
        if duplicates > 0:
            warnings.append(f"{duplicates} duplicate heading texts found")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "stats": self.generate_summary_stats(headings)
        }
