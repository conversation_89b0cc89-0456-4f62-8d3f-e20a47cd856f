"""
Text Processing and Hierarchy Detection Module

This module processes extracted text from Donut model output and detects
hierarchical structure for table of contents generation.
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from loguru import logger


@dataclass
class Heading:
    """Represents a document heading with hierarchy information."""
    text: str
    level: int
    page_number: int
    confidence: float = 1.0
    original_text: str = ""
    
    def __post_init__(self):
        if not self.original_text:
            self.original_text = self.text


class TextProcessor:
    """
    Processes extracted text and detects hierarchical structure for TOC generation.
    """
    
    def __init__(self):
        """Initialize the text processor with default patterns."""
        self.heading_patterns = self._compile_heading_patterns()
        self.noise_patterns = self._compile_noise_patterns()
        
        logger.info("TextProcessor initialized")
    
    def _compile_heading_patterns(self) -> List[Dict[str, Any]]:
        """
        Compile regex patterns for detecting different heading levels.
        
        Returns:
            List of pattern dictionaries with regex and level information
        """
        patterns = [
            # Chapter/Title patterns (Level 1)
            {
                "pattern": re.compile(r'^(CHAPTER\s+\d+|Chapter\s+\d+|PART\s+[IVX]+|Part\s+[IVX]+)[\s\-:]*(.+)$', re.IGNORECASE | re.MULTILINE),
                "level": 1,
                "confidence": 0.95,
                "description": "Chapter/Part titles"
            },
            {
                "pattern": re.compile(r'^(\d+\.?\s+)([A-Z][^.!?]*[^.!?\s])$', re.MULTILINE),
                "level": 1,
                "confidence": 0.9,
                "description": "Numbered sections (1. Title)"
            },
            
            # Section patterns (Level 2)
            {
                "pattern": re.compile(r'^(\d+\.\d+\.?\s+)([A-Z][^.!?]*[^.!?\s])$', re.MULTILINE),
                "level": 2,
                "confidence": 0.9,
                "description": "Numbered subsections (1.1 Title)"
            },
            {
                "pattern": re.compile(r'^([A-Z][A-Z\s]{2,}[A-Z])$', re.MULTILINE),
                "level": 2,
                "confidence": 0.7,
                "description": "ALL CAPS headings"
            },
            
            # Subsection patterns (Level 3)
            {
                "pattern": re.compile(r'^(\d+\.\d+\.\d+\.?\s+)([A-Z][^.!?]*[^.!?\s])$', re.MULTILINE),
                "level": 3,
                "confidence": 0.9,
                "description": "Numbered sub-subsections (1.1.1 Title)"
            },
            {
                "pattern": re.compile(r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*$', re.MULTILINE),
                "level": 3,
                "confidence": 0.6,
                "description": "Title Case headings"
            },
            
            # Lower level patterns (Level 4+)
            {
                "pattern": re.compile(r'^(\d+\.\d+\.\d+\.\d+\.?\s+)([A-Z][^.!?]*[^.!?\s])$', re.MULTILINE),
                "level": 4,
                "confidence": 0.9,
                "description": "Deep numbered sections"
            },
            
            # Generic patterns with lower confidence
            {
                "pattern": re.compile(r'^([A-Z][^.!?]*[^.!?\s])\s*$', re.MULTILINE),
                "level": 2,
                "confidence": 0.4,
                "description": "Generic capitalized lines"
            }
        ]
        
        return patterns
    
    def _compile_noise_patterns(self) -> List[re.Pattern]:
        """
        Compile patterns for filtering out noise/non-heading text.
        
        Returns:
            List of compiled regex patterns for noise detection
        """
        noise_patterns = [
            re.compile(r'^\s*$'),  # Empty lines
            re.compile(r'^page\s+\d+', re.IGNORECASE),  # Page numbers
            re.compile(r'^\d+\s*$'),  # Standalone numbers
            re.compile(r'^[^\w\s]*$'),  # Only punctuation
            re.compile(r'^(figure|table|fig|tab)[\s\d\.:]+', re.IGNORECASE),  # Figure/table captions
            re.compile(r'^(see|refer|reference)\s+', re.IGNORECASE),  # References
            re.compile(r'^\w{1,2}\s*$'),  # Very short words
            re.compile(r'^[a-z]'),  # Lines starting with lowercase (likely body text)
            re.compile(r'\.(com|org|net|edu|gov)', re.IGNORECASE),  # URLs
            re.compile(r'@\w+'),  # Email addresses
        ]
        
        return noise_patterns
    
    def process_pages(self, page_results: List[Dict[str, Any]]) -> List[Heading]:
        """
        Process multiple pages and extract headings.
        
        Args:
            page_results: List of page processing results from Donut model
            
        Returns:
            List of detected headings with hierarchy information
        """
        all_headings = []
        
        logger.info(f"Processing {len(page_results)} pages for heading detection")
        
        for page_result in page_results:
            if not page_result.get("success", False):
                logger.warning(f"Skipping failed page {page_result.get('page_number', '?')}")
                continue
            
            page_number = page_result.get("page_number", 1)
            extracted_text = page_result.get("extracted_text", "")
            
            if not extracted_text.strip():
                logger.debug(f"No text found on page {page_number}")
                continue
            
            # Extract headings from this page
            page_headings = self.extract_headings_from_text(extracted_text, page_number)
            all_headings.extend(page_headings)
            
            logger.debug(f"Found {len(page_headings)} headings on page {page_number}")
        
        # Post-process and refine headings
        refined_headings = self._refine_headings(all_headings)
        
        logger.success(f"Extracted {len(refined_headings)} total headings")
        return refined_headings
    
    def extract_headings_from_text(self, text: str, page_number: int) -> List[Heading]:
        """
        Extract headings from a single page's text.
        
        Args:
            text: Extracted text from the page
            page_number: Page number for reference
            
        Returns:
            List of headings found on this page
        """
        headings = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines and obvious noise
            if self._is_noise(line):
                continue
            
            # Try to match against heading patterns
            heading = self._match_heading_patterns(line, page_number)
            if heading:
                headings.append(heading)
        
        return headings
    
    def _match_heading_patterns(self, line: str, page_number: int) -> Optional[Heading]:
        """
        Try to match a line against heading patterns.
        
        Args:
            line: Text line to analyze
            page_number: Page number for reference
            
        Returns:
            Heading object if match found, None otherwise
        """
        best_match = None
        best_confidence = 0.0
        
        for pattern_info in self.heading_patterns:
            pattern = pattern_info["pattern"]
            level = pattern_info["level"]
            base_confidence = pattern_info["confidence"]
            
            match = pattern.match(line)
            if match:
                # Extract heading text
                if len(match.groups()) >= 2:
                    heading_text = match.group(2).strip()
                elif len(match.groups()) == 1:
                    heading_text = match.group(1).strip()
                else:
                    heading_text = line.strip()
                
                # Calculate confidence based on various factors
                confidence = self._calculate_confidence(
                    heading_text, line, base_confidence, level
                )
                
                if confidence > best_confidence:
                    best_match = Heading(
                        text=heading_text,
                        level=level,
                        page_number=page_number,
                        confidence=confidence,
                        original_text=line
                    )
                    best_confidence = confidence
        
        return best_match
    
    def _calculate_confidence(
        self, 
        heading_text: str, 
        original_line: str, 
        base_confidence: float, 
        level: int
    ) -> float:
        """
        Calculate confidence score for a potential heading.
        
        Args:
            heading_text: Extracted heading text
            original_line: Original line text
            base_confidence: Base confidence from pattern
            level: Detected heading level
            
        Returns:
            Adjusted confidence score
        """
        confidence = base_confidence
        
        # Length-based adjustments
        if len(heading_text) < 3:
            confidence *= 0.3  # Very short headings are suspicious
        elif len(heading_text) > 100:
            confidence *= 0.7  # Very long headings are less likely
        elif 10 <= len(heading_text) <= 50:
            confidence *= 1.1  # Good length range
        
        # Content-based adjustments
        if re.search(r'\d', heading_text):
            confidence *= 1.1  # Numbers often indicate structure
        
        if heading_text.isupper() and len(heading_text) > 20:
            confidence *= 0.8  # Long all-caps might be noise
        
        if re.search(r'[.!?]$', heading_text):
            confidence *= 0.7  # Headings usually don't end with punctuation
        
        # Ensure confidence stays within bounds
        return max(0.0, min(1.0, confidence))
    
    def _is_noise(self, line: str) -> bool:
        """
        Check if a line is likely noise/non-heading content.
        
        Args:
            line: Text line to check
            
        Returns:
            True if line is likely noise
        """
        if not line or len(line.strip()) < 2:
            return True
        
        for noise_pattern in self.noise_patterns:
            if noise_pattern.search(line):
                return True
        
        return False
    
    def _refine_headings(self, headings: List[Heading]) -> List[Heading]:
        """
        Post-process and refine the list of detected headings.
        
        Args:
            headings: Raw list of detected headings
            
        Returns:
            Refined list of headings
        """
        if not headings:
            return headings
        
        # Sort by page number and confidence
        headings.sort(key=lambda h: (h.page_number, -h.confidence))
        
        # Remove duplicates and low-confidence headings
        refined = []
        seen_texts = set()
        
        for heading in headings:
            # Skip very low confidence headings
            if heading.confidence < 0.3:
                continue
            
            # Skip near-duplicates
            normalized_text = re.sub(r'\s+', ' ', heading.text.lower().strip())
            if normalized_text in seen_texts:
                continue
            
            seen_texts.add(normalized_text)
            refined.append(heading)
        
        # Adjust levels based on context
        refined = self._adjust_heading_levels(refined)
        
        return refined
    
    def _adjust_heading_levels(self, headings: List[Heading]) -> List[Heading]:
        """
        Adjust heading levels based on document structure context.
        
        Args:
            headings: List of headings to adjust
            
        Returns:
            List of headings with adjusted levels
        """
        if len(headings) < 2:
            return headings
        
        # Find the minimum level to normalize
        min_level = min(h.level for h in headings)
        
        # Normalize levels to start from 1
        for heading in headings:
            heading.level = heading.level - min_level + 1
        
        return headings
