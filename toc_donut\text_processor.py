"""
Text Processing and Hierarchy Detection Module

This module processes extracted text from Donut model output and detects
hierarchical structure for table of contents generation.
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from loguru import logger


@dataclass
class Heading:
    """Represents a document heading with hierarchy information."""
    text: str
    level: int
    page_number: int
    confidence: float = 1.0
    original_text: str = ""
    
    def __post_init__(self):
        if not self.original_text:
            self.original_text = self.text


class TextProcessor:
    """
    Processes extracted text and detects hierarchical structure for TOC generation.
    """
    
    def __init__(self):
        """Initialize the text processor with default patterns."""
        self.heading_patterns = self._compile_heading_patterns()
        self.noise_patterns = self._compile_noise_patterns()
        
        logger.info("TextProcessor initialized")
    
    def _compile_heading_patterns(self) -> List[Dict[str, Any]]:
        """
        Compile regex patterns for detecting different heading levels.
        
        Returns:
            List of pattern dictionaries with regex and level information
        """
        patterns = [
            # Chapter/Title patterns (Level 1)
            {
                "pattern": re.compile(r'^(CHAPTER\s+\d+|Chapter\s+\d+|PART\s+[IVX]+|Part\s+[IVX]+)[\s\-:]*(.+)$', re.IGNORECASE | re.MULTILINE),
                "level": 1,
                "confidence": 0.95,
                "description": "Chapter/Part titles"
            },
            {
                "pattern": re.compile(r'^(\d+\.?\s+)([A-Z].{2,})$', re.MULTILINE),
                "level": 1,
                "confidence": 0.9,
                "description": "Numbered sections (1. Title)"
            },

            # Section patterns (Level 2)
            {
                "pattern": re.compile(r'^(\d+\.\d+\.?\s+)([A-Z].{2,})$', re.MULTILINE),
                "level": 2,
                "confidence": 0.9,
                "description": "Numbered subsections (1.1 Title)"
            },
            {
                "pattern": re.compile(r'^([A-Z][A-Z\s]{2,})$', re.MULTILINE),
                "level": 2,
                "confidence": 0.8,
                "description": "ALL CAPS headings"
            },

            # Subsection patterns (Level 3)
            {
                "pattern": re.compile(r'^(\d+\.\d+\.\d+\.?\s+)([A-Z].{2,})$', re.MULTILINE),
                "level": 3,
                "confidence": 0.9,
                "description": "Numbered sub-subsections (1.1.1 Title)"
            },
            {
                "pattern": re.compile(r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*$', re.MULTILINE),
                "level": 3,
                "confidence": 0.7,
                "description": "Title Case headings"
            },

            # Academic paper patterns
            {
                "pattern": re.compile(r'^(ABSTRACT|Abstract|INTRODUCTION|Introduction|METHODOLOGY|Methodology|RESULTS|Results|CONCLUSION|Conclusion|REFERENCES|References)$', re.MULTILINE),
                "level": 1,
                "confidence": 0.95,
                "description": "Academic paper sections"
            },
            {
                "pattern": re.compile(r'^(BACKGROUND|Background|RELATED WORK|Related Work|DISCUSSION|Discussion|ACKNOWLEDGMENTS|Acknowledgments)$', re.MULTILINE),
                "level": 1,
                "confidence": 0.9,
                "description": "Academic paper sections"
            },

            # Roman numerals
            {
                "pattern": re.compile(r'^([IVX]+\.?\s+)([A-Z].{2,})$', re.MULTILINE),
                "level": 1,
                "confidence": 0.8,
                "description": "Roman numeral sections"
            },

            # Letter sections
            {
                "pattern": re.compile(r'^([A-Z]\.?\s+)([A-Z].{2,})$', re.MULTILINE),
                "level": 2,
                "confidence": 0.7,
                "description": "Letter sections (A. Title)"
            },

            # Lower level patterns (Level 4+)
            {
                "pattern": re.compile(r'^(\d+\.\d+\.\d+\.\d+\.?\s+)([A-Z].{2,})$', re.MULTILINE),
                "level": 4,
                "confidence": 0.9,
                "description": "Deep numbered sections"
            },

            # Generic patterns with lower confidence - more flexible
            {
                "pattern": re.compile(r'^([A-Z][^.!?]{3,})\s*$', re.MULTILINE),
                "level": 2,
                "confidence": 0.5,
                "description": "Generic capitalized lines"
            },

            # Bold or emphasized text (common in PDFs)
            {
                "pattern": re.compile(r'^([A-Z][a-z].{3,})\s*$', re.MULTILINE),
                "level": 3,
                "confidence": 0.4,
                "description": "Potential headings starting with capital"
            }
        ]
        
        return patterns
    
    def _compile_noise_patterns(self) -> List[re.Pattern]:
        """
        Compile patterns for filtering out noise/non-heading text.
        
        Returns:
            List of compiled regex patterns for noise detection
        """
        noise_patterns = [
            re.compile(r'^\s*$'),  # Empty lines
            re.compile(r'^page\s+\d+', re.IGNORECASE),  # Page numbers
            re.compile(r'^\d+\s*$'),  # Standalone numbers
            re.compile(r'^[^\w\s]*$'),  # Only punctuation
            re.compile(r'^(figure|table|fig|tab)[\s\d\.:]+', re.IGNORECASE),  # Figure/table captions
            re.compile(r'^(see|refer|reference)\s+', re.IGNORECASE),  # References
            re.compile(r'^\w{1,2}\s*$'),  # Very short words
            re.compile(r'^[a-z]'),  # Lines starting with lowercase (likely body text)
            re.compile(r'\.(com|org|net|edu|gov)', re.IGNORECASE),  # URLs
            re.compile(r'@\w+'),  # Email addresses
        ]
        
        return noise_patterns
    
    def process_pages(self, page_results: List[Dict[str, Any]]) -> List[Heading]:
        """
        Process multiple pages and extract headings.
        
        Args:
            page_results: List of page processing results from Donut model
            
        Returns:
            List of detected headings with hierarchy information
        """
        all_headings = []
        
        logger.info(f"Processing {len(page_results)} pages for heading detection")
        
        for page_result in page_results:
            if not page_result.get("success", False):
                logger.warning(f"Skipping failed page {page_result.get('page_number', '?')}")
                continue
            
            page_number = page_result.get("page_number", 1)
            extracted_text = page_result.get("extracted_text", "")
            
            if not extracted_text.strip():
                logger.debug(f"No text found on page {page_number}")
                continue
            
            # Extract headings from this page
            page_headings = self.extract_headings_from_text(extracted_text, page_number)
            all_headings.extend(page_headings)
            
            logger.debug(f"Found {len(page_headings)} headings on page {page_number}")
        
        # Post-process and refine headings
        refined_headings = self._refine_headings(all_headings)
        
        logger.success(f"Extracted {len(refined_headings)} total headings")
        return refined_headings
    
    def extract_headings_from_text(self, text: str, page_number: int) -> List[Heading]:
        """
        Extract headings from a single page's text.

        Args:
            text: Extracted text from the page
            page_number: Page number for reference

        Returns:
            List of headings found on this page
        """
        headings = []
        lines = text.split('\n')

        logger.debug(f"Processing {len(lines)} lines from page {page_number}")
        logger.debug(f"Sample lines: {lines[:5]}")

        for i, line in enumerate(lines):
            line = line.strip()

            # Skip empty lines and obvious noise
            if self._is_noise(line):
                continue

            logger.debug(f"Checking line {i}: '{line}'")

            # Try to match against heading patterns
            heading = self._match_heading_patterns(line, page_number)
            if heading:
                logger.debug(f"Found heading: '{heading.text}' (Level {heading.level}, Confidence: {heading.confidence:.3f})")
                headings.append(heading)
            else:
                # Fallback: check if line could be a heading based on simple heuristics
                fallback_heading = self._check_fallback_heading(line, page_number)
                if fallback_heading:
                    logger.debug(f"Found fallback heading: '{fallback_heading.text}' (Level {fallback_heading.level}, Confidence: {fallback_heading.confidence:.3f})")
                    headings.append(fallback_heading)

        logger.debug(f"Found {len(headings)} headings on page {page_number}")
        return headings
    
    def _match_heading_patterns(self, line: str, page_number: int) -> Optional[Heading]:
        """
        Try to match a line against heading patterns.
        
        Args:
            line: Text line to analyze
            page_number: Page number for reference
            
        Returns:
            Heading object if match found, None otherwise
        """
        best_match = None
        best_confidence = 0.0
        
        for pattern_info in self.heading_patterns:
            pattern = pattern_info["pattern"]
            level = pattern_info["level"]
            base_confidence = pattern_info["confidence"]
            
            match = pattern.match(line)
            if match:
                # Extract heading text
                if len(match.groups()) >= 2:
                    heading_text = match.group(2).strip()
                elif len(match.groups()) == 1:
                    heading_text = match.group(1).strip()
                else:
                    heading_text = line.strip()
                
                # Calculate confidence based on various factors
                confidence = self._calculate_confidence(
                    heading_text, line, base_confidence, level
                )
                
                if confidence > best_confidence:
                    best_match = Heading(
                        text=heading_text,
                        level=level,
                        page_number=page_number,
                        confidence=confidence,
                        original_text=line
                    )
                    best_confidence = confidence
        
        return best_match

    def _check_fallback_heading(self, line: str, page_number: int) -> Optional[Heading]:
        """
        Fallback method to detect potential headings using simple heuristics.

        Args:
            line: Text line to analyze
            page_number: Page number for reference

        Returns:
            Heading object if line seems like a heading, None otherwise
        """
        # Skip if line is too short or too long
        if len(line) < 3 or len(line) > 100:
            return None

        # Skip if line contains too many lowercase words (likely body text)
        words = line.split()
        if len(words) > 3:  # More strict for longer lines
            lowercase_words = sum(1 for word in words if word.islower() and len(word) > 2)
            if lowercase_words > len(words) * 0.4:  # More than 40% lowercase words
                return None

        # Skip if line looks like a sentence (contains common sentence patterns)
        sentence_indicators = ['this is', 'the ', 'that ', 'with ', 'from ', 'have ', 'will ', 'can ', 'should ', 'would ']
        if any(indicator in line.lower() for indicator in sentence_indicators):
            return None

        # Check for potential heading characteristics
        confidence = 0.0
        level = 2  # Default level

        # Starts with capital letter
        if line[0].isupper():
            confidence += 0.2

        # Short line (potential heading)
        if len(line) <= 50:
            confidence += 0.2

        # Contains numbers (section numbering)
        if re.search(r'\d', line):
            confidence += 0.2
            level = 1

        # All caps (but not too long)
        if line.isupper() and len(line) <= 30:
            confidence += 0.3
            level = 1

        # Title case
        if line.istitle():
            confidence += 0.2

        # Ends without punctuation (typical for headings)
        if not re.search(r'[.!?]$', line):
            confidence += 0.1

        # Contains common heading words
        heading_words = ['introduction', 'conclusion', 'abstract', 'methodology', 'results', 'discussion', 'background', 'summary', 'overview', 'analysis']
        if any(word in line.lower() for word in heading_words):
            confidence += 0.4
            level = 1

        # Only return if confidence is reasonable
        if confidence >= 0.3:
            return Heading(
                text=line.strip(),
                level=level,
                page_number=page_number,
                confidence=confidence,
                original_text=line
            )

        return None

    def _calculate_confidence(
        self,
        heading_text: str,
        original_line: str,
        base_confidence: float,
        level: int
    ) -> float:
        """
        Calculate confidence score for a potential heading.

        Args:
            heading_text: Extracted heading text
            original_line: Original line text
            base_confidence: Base confidence from pattern
            level: Detected heading level

        Returns:
            Adjusted confidence score
        """
        confidence = base_confidence

        # Length-based adjustments - more lenient
        if len(heading_text) < 2:
            confidence *= 0.2  # Very short headings are suspicious
        elif len(heading_text) > 150:
            confidence *= 0.6  # Very long headings are less likely
        elif 5 <= len(heading_text) <= 80:
            confidence *= 1.2  # Good length range - expanded

        # Content-based adjustments
        if re.search(r'\d', heading_text):
            confidence *= 1.2  # Numbers often indicate structure

        if heading_text.isupper() and len(heading_text) > 30:
            confidence *= 0.7  # Long all-caps might be noise - more lenient

        if re.search(r'[.!?]$', heading_text):
            confidence *= 0.8  # Headings usually don't end with punctuation - more lenient

        # Boost confidence for common academic terms
        academic_terms = ['introduction', 'abstract', 'methodology', 'results', 'conclusion', 'discussion', 'background', 'related work']
        if any(term in heading_text.lower() for term in academic_terms):
            confidence *= 1.3

        # Ensure confidence stays within bounds
        return max(0.0, min(1.0, confidence))
    
    def _is_noise(self, line: str) -> bool:
        """
        Check if a line is likely noise/non-heading content.
        
        Args:
            line: Text line to check
            
        Returns:
            True if line is likely noise
        """
        if not line or len(line.strip()) < 2:
            return True
        
        for noise_pattern in self.noise_patterns:
            if noise_pattern.search(line):
                return True
        
        return False
    
    def _refine_headings(self, headings: List[Heading]) -> List[Heading]:
        """
        Post-process and refine the list of detected headings.
        
        Args:
            headings: Raw list of detected headings
            
        Returns:
            Refined list of headings
        """
        if not headings:
            return headings
        
        # Sort by page number and confidence
        headings.sort(key=lambda h: (h.page_number, -h.confidence))
        
        # Remove duplicates and low-confidence headings
        refined = []
        seen_texts = set()
        
        for heading in headings:
            # Skip very low confidence headings - more lenient threshold
            if heading.confidence < 0.2:
                continue
            
            # Skip near-duplicates
            normalized_text = re.sub(r'\s+', ' ', heading.text.lower().strip())
            if normalized_text in seen_texts:
                continue
            
            seen_texts.add(normalized_text)
            refined.append(heading)
        
        # Adjust levels based on context
        refined = self._adjust_heading_levels(refined)
        
        return refined
    
    def _adjust_heading_levels(self, headings: List[Heading]) -> List[Heading]:
        """
        Adjust heading levels based on document structure context.
        
        Args:
            headings: List of headings to adjust
            
        Returns:
            List of headings with adjusted levels
        """
        if len(headings) < 2:
            return headings
        
        # Find the minimum level to normalize
        min_level = min(h.level for h in headings)
        
        # Normalize levels to start from 1
        for heading in headings:
            heading.level = heading.level - min_level + 1
        
        return headings
