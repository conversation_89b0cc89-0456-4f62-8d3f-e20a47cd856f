# API Reference

This document provides detailed information about the toc-donut API.

## Core Classes

### PDFProcessor

Handles PDF to image conversion with preprocessing for optimal Donut model input.

#### Constructor

```python
PDFProcessor(
    dpi: int = 200,
    output_format: str = "PNG",
    max_width: int = 2048,
    max_height: int = 2048,
    poppler_path: Optional[str] = None
)
```

**Parameters:**
- `dpi`: Resolution for PDF to image conversion (default: 200)
- `output_format`: Image format (default: PNG)
- `max_width`: Maximum image width for resizing (default: 2048)
- `max_height`: Maximum image height for resizing (default: 2048)
- `poppler_path`: Path to poppler binaries (optional)

#### Methods

##### convert_pdf_to_images()

```python
convert_pdf_to_images(
    pdf_path: Union[str, Path],
    output_dir: Optional[Union[str, Path]] = None,
    page_range: Optional[Tuple[int, int]] = None
) -> List[Path]
```

Convert PDF pages to images.

**Parameters:**
- `pdf_path`: Path to the PDF file
- `output_dir`: Directory to save images (optional, uses temp dir if None)
- `page_range`: Tuple of (start_page, end_page) for partial conversion

**Returns:** List of paths to generated image files

**Raises:**
- `FileNotFoundError`: If PDF file doesn't exist
- `ValueError`: If PDF is invalid or conversion fails

##### get_pdf_info()

```python
get_pdf_info(pdf_path: Union[str, Path]) -> dict
```

Get basic information about the PDF.

**Returns:** Dictionary with PDF information including page count, title, author, etc.

##### cleanup_temp_files()

```python
cleanup_temp_files(image_paths: List[Path]) -> None
```

Clean up temporary image files.

---

### DonutModel

Wrapper for Hugging Face Donut model for document understanding.

#### Constructor

```python
DonutModel(
    model_name: str = "naver-clova-ix/donut-base-finetuned-docvqa",
    device: Optional[str] = None,
    cache_dir: Optional[str] = None
)
```

**Parameters:**
- `model_name`: Hugging Face model name/path
- `device`: Device to run model on (auto-detected if None)
- `cache_dir`: Directory to cache model files

#### Methods

##### extract_text_from_image()

```python
extract_text_from_image(
    image: Union[str, Path, Image.Image],
    task_prompt: str = "<s_docvqa><s_question>What is the text content of this document?</s_question><s_answer>",
    max_length: int = 512,
    num_beams: int = 1,
    temperature: float = 1.0
) -> str
```

Extract text from a document image using Donut.

**Parameters:**
- `image`: PIL Image, path to image file, or image data
- `task_prompt`: Task-specific prompt for the model
- `max_length`: Maximum length of generated text
- `num_beams`: Number of beams for beam search
- `temperature`: Sampling temperature

**Returns:** Extracted text content

##### process_document_pages()

```python
process_document_pages(
    image_paths: List[Path],
    extract_headings: bool = True,
    progress_callback: Optional[callable] = None
) -> List[Dict[str, Any]]
```

Process multiple document pages and extract content.

**Parameters:**
- `image_paths`: List of paths to image files
- `extract_headings`: Whether to focus on heading extraction
- `progress_callback`: Optional callback for progress updates

**Returns:** List of dictionaries with page content

---

### TextProcessor

Processes extracted text and detects hierarchical structure for TOC generation.

#### Constructor

```python
TextProcessor()
```

#### Methods

##### process_pages()

```python
process_pages(page_results: List[Dict[str, Any]]) -> List[Heading]
```

Process multiple pages and extract headings.

**Parameters:**
- `page_results`: List of page processing results from Donut model

**Returns:** List of detected headings with hierarchy information

##### extract_headings_from_text()

```python
extract_headings_from_text(text: str, page_number: int) -> List[Heading]
```

Extract headings from a single page's text.

**Parameters:**
- `text`: Extracted text from the page
- `page_number`: Page number for reference

**Returns:** List of headings found on this page

---

### TOCGenerator

Generates table of contents in various formats from hierarchical headings.

#### Constructor

```python
TOCGenerator()
```

#### Methods

##### generate_toc_json()

```python
generate_toc_json(
    headings: List[Heading], 
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]
```

Generate table of contents in JSON format.

**Parameters:**
- `headings`: List of hierarchical headings
- `metadata`: Optional metadata about the document

**Returns:** Dictionary representing the TOC in JSON format

##### generate_toc_markdown()

```python
generate_toc_markdown(
    headings: List[Heading], 
    metadata: Optional[Dict[str, Any]] = None,
    include_page_numbers: bool = True,
    include_confidence: bool = False
) -> str
```

Generate table of contents in Markdown format.

**Parameters:**
- `headings`: List of hierarchical headings
- `metadata`: Optional metadata about the document
- `include_page_numbers`: Whether to include page numbers
- `include_confidence`: Whether to include confidence scores

**Returns:** Markdown-formatted table of contents

##### save_toc_json()

```python
save_toc_json(
    headings: List[Heading], 
    output_path: Path, 
    metadata: Optional[Dict[str, Any]] = None
) -> None
```

Generate and save TOC in JSON format to file.

##### save_toc_markdown()

```python
save_toc_markdown(
    headings: List[Heading], 
    output_path: Path, 
    metadata: Optional[Dict[str, Any]] = None,
    include_page_numbers: bool = True,
    include_confidence: bool = False
) -> None
```

Generate and save TOC in Markdown format to file.

##### generate_summary_stats()

```python
generate_summary_stats(headings: List[Heading]) -> Dict[str, Any]
```

Generate summary statistics about the TOC.

**Returns:** Dictionary with summary statistics

##### validate_toc_structure()

```python
validate_toc_structure(headings: List[Heading]) -> Dict[str, Any]
```

Validate the TOC structure and identify potential issues.

**Returns:** Dictionary with validation results

---

## Data Classes

### Heading

Represents a document heading with hierarchy information.

#### Attributes

- `text: str` - The heading text
- `level: int` - Hierarchical level (1, 2, 3, etc.)
- `page_number: int` - Page number where heading appears
- `confidence: float` - Confidence score (0.0 to 1.0)
- `original_text: str` - Original extracted text

#### Constructor

```python
Heading(
    text: str,
    level: int,
    page_number: int,
    confidence: float = 1.0,
    original_text: str = ""
)
```

---

## Utility Functions

The `toc_donut.utils` module provides various utility functions:

- `validate_pdf_file(file_path)` - Validate if a file is a valid PDF
- `sanitize_filename(filename)` - Sanitize filename for filesystem safety
- `calculate_file_hash(file_path)` - Calculate SHA-256 hash of a file
- `format_file_size(size_bytes)` - Format file size in human-readable format
- `clean_text(text)` - Clean and normalize text content
- `ensure_directory(directory)` - Ensure a directory exists
- `get_available_models()` - Get list of available Donut models
- `validate_model_name(model_name)` - Validate model name format

---

## Error Handling

The library uses custom exceptions and standard Python exceptions:

- `FileNotFoundError` - When input files don't exist
- `ValueError` - For invalid parameters or data
- `RuntimeError` - For model loading or processing failures

All functions include proper error handling and logging using the `loguru` library.
