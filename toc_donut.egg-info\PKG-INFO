Metadata-Version: 2.1
Name: toc-donut
Version: 1.0.0
Summary: Table of Contents Generator using Hugging Face Donut model
Home-page: https://github.com/example/toc-donut
Author: Donut Project Team
Author-email: <EMAIL>
Project-URL: Bug Reports, https://github.com/example/toc-donut/issues
Project-URL: Source, https://github.com/example/toc-donut
Project-URL: Documentation, https://github.com/example/toc-donut#readme
Keywords: pdf,table-of-contents,donut,document-understanding,nlp,ai
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Text Processing :: Markup
Classifier: Topic :: Utilities
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: torch>=1.9.0
Requires-Dist: transformers>=4.21.0
Requires-Dist: tokenizers>=0.13.0
Requires-Dist: Pillow>=9.0.0
Requires-Dist: pdf2image>=1.16.0
Requires-Dist: PyPDF2>=2.0.0
Requires-Dist: click>=8.0.0
Requires-Dist: tqdm>=4.64.0
Requires-Dist: rich>=12.0.0
Requires-Dist: numpy>=1.21.0
Requires-Dist: pandas>=1.3.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: loguru>=0.6.0
Requires-Dist: pytest>=7.0.0
Requires-Dist: pytest-cov>=4.0.0
Requires-Dist: black>=22.0.0
Requires-Dist: flake8>=5.0.0
Requires-Dist: mypy>=0.991
Requires-Dist: accelerate>=0.20.0

# toc-donut: Table of Contents Generator using Donut

A production-quality command-line tool that automatically extracts hierarchical section headings from PDF documents using the Hugging Face Donut (Document Understanding Transformer) model and generates structured Table of Contents in JSON and Markdown formats.

## Features

- 🔍 **Automatic TOC Extraction**: Extract hierarchical headings (Title, H1, H2, etc.) from PDF documents
- 🤖 **AI-Powered**: Uses Hugging Face Donut model for document understanding
- 📄 **Multiple Output Formats**: Generate TOC in JSON and Markdown formats
- 🖼️ **Image Processing**: Converts PDF pages to images for optimal model input
- 🛠️ **CLI Interface**: Easy-to-use command-line tool
- ⚡ **Production Ready**: Robust error handling and validation

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd toc-donut

# Install dependencies
pip install -r requirements.txt

# Install the package
pip install -e .
```

## Quick Start

```bash
# Generate TOC in JSON format
toc-donut input/document.pdf --output toc.json --format json

# Generate TOC in Markdown format
toc-donut input/document.pdf --output toc.md --format markdown

# Generate both formats
toc-donut input/document.pdf --output-dir ./output --format both
```

## Usage

```bash
toc-donut [OPTIONS] INPUT_PDF

Options:
  --output, -o TEXT       Output file path
  --output-dir, -d TEXT   Output directory (for multiple formats)
  --format, -f TEXT       Output format: json, markdown, or both [default: json]
  --verbose, -v           Enable verbose logging
  --help                  Show this message and exit
```

## Project Structure

```
toc-donut/
├── toc_donut/              # Main package
│   ├── __init__.py
│   ├── cli.py              # Command-line interface
│   ├── pdf_processor.py    # PDF to image conversion
│   ├── donut_model.py      # Donut model integration
│   ├── text_processor.py   # Text processing and hierarchy detection
│   ├── toc_generator.py    # TOC generation logic
│   └── utils.py            # Utility functions
├── tests/                  # Test suite
├── examples/               # Usage examples
├── input/                  # Sample input files
├── requirements.txt        # Dependencies
├── setup.py               # Package setup
└── README.md              # This file
```

## Requirements

- Python 3.8+
- PyTorch
- Transformers (Hugging Face)
- PIL/Pillow
- pdf2image
- poppler-utils (system dependency)

## License

MIT License

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
