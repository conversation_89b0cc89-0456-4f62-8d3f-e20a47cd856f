"""
Tests for TOC generation functionality.
"""

import json
import tempfile
from pathlib import Path

import pytest
from toc_donut.toc_generator import TOCGenerator
from toc_donut.text_processor import Heading


class TestTOCGenerator:
    """Test cases for TOCGenerator class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.generator = TOCGenerator()
        self.sample_headings = [
            Heading("Introduction", 1, 1, 0.9),
            Heading("Background", 2, 1, 0.8),
            Heading("Related Work", 2, 2, 0.8),
            Heading("Methodology", 1, 3, 0.9),
            Heading("Data Collection", 2, 3, 0.7),
            Heading("Analysis", 2, 4, 0.8),
            Heading("Results", 1, 5, 0.9),
        ]
    
    def test_initialization(self):
        """Test TOCGenerator initialization."""
        assert self.generator is not None
    
    def test_generate_toc_json_empty(self):
        """Test JSON generation with empty headings."""
        toc_json = self.generator.generate_toc_json([])
        
        assert "metadata" in toc_json
        assert "table_of_contents" in toc_json
        assert "headings_flat" in toc_json
        assert toc_json["metadata"]["total_headings"] == 0
        assert len(toc_json["table_of_contents"]) == 0
        assert len(toc_json["headings_flat"]) == 0
    
    def test_generate_toc_json_with_headings(self):
        """Test JSON generation with sample headings."""
        toc_json = self.generator.generate_toc_json(self.sample_headings)
        
        assert toc_json["metadata"]["total_headings"] == len(self.sample_headings)
        assert toc_json["metadata"]["max_level"] == 2
        assert len(toc_json["headings_flat"]) == len(self.sample_headings)
        assert len(toc_json["table_of_contents"]) > 0
        
        # Check that hierarchical structure is created
        assert isinstance(toc_json["table_of_contents"], list)
        
        # Verify flat headings structure
        for i, heading_data in enumerate(toc_json["headings_flat"]):
            original_heading = self.sample_headings[i]
            assert heading_data["text"] == original_heading.text
            assert heading_data["level"] == original_heading.level
            assert heading_data["page"] == original_heading.page_number
            assert "confidence" in heading_data
    
    def test_generate_toc_markdown_empty(self):
        """Test Markdown generation with empty headings."""
        markdown = self.generator.generate_toc_markdown([])
        
        assert "# Table of Contents" in markdown
        assert "No headings detected" in markdown
    
    def test_generate_toc_markdown_with_headings(self):
        """Test Markdown generation with sample headings."""
        markdown = self.generator.generate_toc_markdown(self.sample_headings)
        
        assert "# Table of Contents" in markdown
        assert "Introduction" in markdown
        assert "Methodology" in markdown
        assert "Page 1" in markdown  # Page numbers should be included by default
        
        # Check indentation for different levels
        lines = markdown.split('\n')
        intro_line = next((line for line in lines if "Introduction" in line), None)
        background_line = next((line for line in lines if "Background" in line), None)
        
        assert intro_line is not None
        assert background_line is not None
        
        # Level 2 headings should be indented more than level 1
        intro_indent = len(intro_line) - len(intro_line.lstrip())
        background_indent = len(background_line) - len(background_line.lstrip())
        assert background_indent > intro_indent
    
    def test_generate_toc_markdown_options(self):
        """Test Markdown generation with different options."""
        # Without page numbers
        markdown_no_pages = self.generator.generate_toc_markdown(
            self.sample_headings, 
            include_page_numbers=False
        )
        assert "Page" not in markdown_no_pages
        
        # With confidence scores
        markdown_with_confidence = self.generator.generate_toc_markdown(
            self.sample_headings, 
            include_confidence=True
        )
        assert "Confidence:" in markdown_with_confidence
    
    def test_build_hierarchical_structure_empty(self):
        """Test hierarchical structure building with empty input."""
        structure = self.generator._build_hierarchical_structure([])
        assert structure == []
    
    def test_build_hierarchical_structure_flat(self):
        """Test hierarchical structure building with flat headings."""
        flat_headings = [
            Heading("First", 1, 1, 0.9),
            Heading("Second", 1, 2, 0.9),
            Heading("Third", 1, 3, 0.9),
        ]
        
        structure = self.generator._build_hierarchical_structure(flat_headings)
        
        assert len(structure) == 3
        for item in structure:
            assert item["level"] == 1
            assert len(item["children"]) == 0
    
    def test_build_hierarchical_structure_nested(self):
        """Test hierarchical structure building with nested headings."""
        structure = self.generator._build_hierarchical_structure(self.sample_headings)
        
        # Should have 3 top-level items (Introduction, Methodology, Results)
        top_level = [item for item in structure if item["level"] == 1]
        assert len(top_level) == 3
        
        # Introduction should have children
        intro_item = next((item for item in structure if item["text"] == "Introduction"), None)
        assert intro_item is not None
        assert len(intro_item["children"]) > 0
    
    def test_generate_summary_stats_empty(self):
        """Test summary statistics with empty headings."""
        stats = self.generator.generate_summary_stats([])
        
        assert stats["total_headings"] == 0
        assert stats["levels"] == {}
        assert stats["pages"] == {}
        assert stats["avg_confidence"] == 0.0
    
    def test_generate_summary_stats_with_data(self):
        """Test summary statistics with sample data."""
        stats = self.generator.generate_summary_stats(self.sample_headings)
        
        assert stats["total_headings"] == len(self.sample_headings)
        assert stats["max_level"] == 2
        assert stats["min_level"] == 1
        assert 1 in stats["levels"]
        assert 2 in stats["levels"]
        assert stats["avg_confidence"] > 0
        assert "page_range" in stats
    
    def test_validate_toc_structure_empty(self):
        """Test TOC structure validation with empty input."""
        validation = self.generator.validate_toc_structure([])
        
        assert not validation["valid"]
        assert "No headings found" in validation["issues"][0]
    
    def test_validate_toc_structure_valid(self):
        """Test TOC structure validation with valid input."""
        validation = self.generator.validate_toc_structure(self.sample_headings)
        
        assert validation["valid"]
        assert len(validation["issues"]) == 0
        assert "stats" in validation
    
    def test_validate_toc_structure_warnings(self):
        """Test TOC structure validation with potential issues."""
        problematic_headings = [
            Heading("Good Heading", 1, 1, 0.9),
            Heading("A", 2, 1, 0.3),  # Short and low confidence
            Heading("Good Heading", 3, 2, 0.8),  # Duplicate text
        ]
        
        validation = self.generator.validate_toc_structure(problematic_headings)
        
        assert validation["valid"]  # Should still be valid
        assert len(validation["warnings"]) > 0  # But should have warnings
    
    def test_save_toc_json(self):
        """Test saving TOC to JSON file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test_toc.json"
            
            self.generator.save_toc_json(self.sample_headings, output_path)
            
            assert output_path.exists()
            
            # Verify content
            with open(output_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            assert "metadata" in data
            assert "table_of_contents" in data
            assert data["metadata"]["total_headings"] == len(self.sample_headings)
    
    def test_save_toc_markdown(self):
        """Test saving TOC to Markdown file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test_toc.md"
            
            self.generator.save_toc_markdown(self.sample_headings, output_path)
            
            assert output_path.exists()
            
            # Verify content
            with open(output_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            assert "# Table of Contents" in content
            assert "Introduction" in content
            assert "Methodology" in content


if __name__ == "__main__":
    pytest.main([__file__])
