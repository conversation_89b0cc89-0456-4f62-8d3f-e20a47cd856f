"""
PDF to Image Conversion Module

This module handles the conversion of PDF documents to images for processing
by the Donut model. It includes robust error handling and image preprocessing.
"""

import os
import tempfile
from pathlib import Path
from typing import List, Optional, Tuple, Union
import logging

from PIL import Image
import pdf2image
from loguru import logger


class PDFProcessor:
    """
    Handles PDF to image conversion with preprocessing for optimal Donut model input.
    """
    
    def __init__(
        self,
        dpi: int = 300,
        output_format: str = "PNG",
        max_width: int = 2048,
        max_height: int = 2048,
        poppler_path: Optional[str] = None
    ):
        """
        Initialize the PDF processor.
        
        Args:
            dpi: Resolution for PDF to image conversion (default: 200)
            output_format: Image format (default: PNG)
            max_width: Maximum image width for resizing (default: 2048)
            max_height: Maximum image height for resizing (default: 2048)
            poppler_path: Path to poppler binaries (optional)
        """
        self.dpi = dpi
        self.output_format = output_format.upper()
        self.max_width = max_width
        self.max_height = max_height
        self.poppler_path = poppler_path
        
        logger.info(f"PDFProcessor initialized with DPI={dpi}, format={output_format}")
    
    def convert_pdf_to_images(
        self,
        pdf_path: Union[str, Path],
        output_dir: Optional[Union[str, Path]] = None,
        page_range: Optional[Tuple[int, int]] = None
    ) -> List[Path]:
        """
        Convert PDF pages to images.
        
        Args:
            pdf_path: Path to the PDF file
            output_dir: Directory to save images (optional, uses temp dir if None)
            page_range: Tuple of (start_page, end_page) for partial conversion
            
        Returns:
            List of paths to generated image files
            
        Raises:
            FileNotFoundError: If PDF file doesn't exist
            ValueError: If PDF is invalid or conversion fails
        """
        pdf_path = Path(pdf_path)
        
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        if not pdf_path.suffix.lower() == '.pdf':
            raise ValueError(f"File is not a PDF: {pdf_path}")
        
        # Setup output directory
        if output_dir is None:
            output_dir = Path(tempfile.mkdtemp(prefix="toc_donut_"))
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Converting PDF to images: {pdf_path}")
        logger.info(f"Output directory: {output_dir}")
        
        try:
            # Convert PDF to images
            images = pdf2image.convert_from_path(
                pdf_path,
                dpi=self.dpi,
                poppler_path=self.poppler_path,
                first_page=page_range[0] if page_range else None,
                last_page=page_range[1] if page_range else None
            )
            
            if not images:
                raise ValueError(f"No images generated from PDF: {pdf_path}")
            
            logger.info(f"Generated {len(images)} images from PDF")
            
            # Save and preprocess images
            image_paths = []
            for i, image in enumerate(images):
                page_num = (page_range[0] if page_range else 1) + i
                image_path = output_dir / f"page_{page_num:04d}.{self.output_format.lower()}"
                
                # Preprocess image
                processed_image = self._preprocess_image(image)
                processed_image.save(image_path, self.output_format)
                
                image_paths.append(image_path)
                logger.debug(f"Saved page {page_num} to {image_path}")
            
            logger.success(f"Successfully converted {len(image_paths)} pages")
            return image_paths
            
        except Exception as e:
            logger.error(f"Failed to convert PDF to images: {e}")
            raise ValueError(f"PDF conversion failed: {e}")
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        Preprocess image for optimal Donut model input.
        
        Args:
            image: PIL Image object
            
        Returns:
            Preprocessed PIL Image
        """
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize if image is too large
        width, height = image.size
        if width > self.max_width or height > self.max_height:
            # Calculate scaling factor to maintain aspect ratio
            scale_factor = min(self.max_width / width, self.max_height / height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            logger.debug(f"Resized image from {width}x{height} to {new_width}x{new_height}")
        
        return image
    
    def get_pdf_info(self, pdf_path: Union[str, Path]) -> dict:
        """
        Get basic information about the PDF.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Dictionary with PDF information
        """
        pdf_path = Path(pdf_path)
        
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        try:
            # Get page count using pdf2image
            info = pdf2image.pdfinfo_from_path(pdf_path, poppler_path=self.poppler_path)
            
            return {
                "file_path": str(pdf_path),
                "file_size": pdf_path.stat().st_size,
                "page_count": info.get("Pages", 0),
                "title": info.get("Title", ""),
                "author": info.get("Author", ""),
                "creator": info.get("Creator", ""),
                "producer": info.get("Producer", ""),
                "creation_date": info.get("CreationDate", ""),
                "modification_date": info.get("ModDate", "")
            }
            
        except Exception as e:
            logger.error(f"Failed to get PDF info: {e}")
            return {
                "file_path": str(pdf_path),
                "file_size": pdf_path.stat().st_size,
                "error": str(e)
            }
    
    def cleanup_temp_files(self, image_paths: List[Path]) -> None:
        """
        Clean up temporary image files.
        
        Args:
            image_paths: List of image file paths to delete
        """
        for image_path in image_paths:
            try:
                if image_path.exists():
                    image_path.unlink()
                    logger.debug(f"Deleted temporary file: {image_path}")
            except Exception as e:
                logger.warning(f"Failed to delete temporary file {image_path}: {e}")
        
        # Try to remove the temporary directory if it's empty
        try:
            if image_paths:
                temp_dir = image_paths[0].parent
                if temp_dir.exists() and not any(temp_dir.iterdir()):
                    temp_dir.rmdir()
                    logger.debug(f"Removed temporary directory: {temp_dir}")
        except Exception as e:
            logger.warning(f"Failed to remove temporary directory: {e}")
