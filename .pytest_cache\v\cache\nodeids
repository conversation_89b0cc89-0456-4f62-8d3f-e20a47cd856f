["tests/test_text_processor.py::TestHeading::test_heading_creation", "tests/test_text_processor.py::TestHeading::test_heading_with_original_text", "tests/test_text_processor.py::TestTextProcessor::test_confidence_calculation", "tests/test_text_processor.py::TestTextProcessor::test_duplicate_removal", "tests/test_text_processor.py::TestTextProcessor::test_extract_headings_from_text_simple", "tests/test_text_processor.py::TestTextProcessor::test_extract_headings_with_chapters", "tests/test_text_processor.py::TestTextProcessor::test_heading_level_adjustment", "tests/test_text_processor.py::TestTextProcessor::test_initialization", "tests/test_text_processor.py::TestTextProcessor::test_low_confidence_filtering", "tests/test_text_processor.py::TestTextProcessor::test_noise_filtering", "tests/test_text_processor.py::TestTextProcessor::test_process_pages_empty", "tests/test_text_processor.py::TestTextProcessor::test_process_pages_failed", "tests/test_toc_generator.py::TestTOCGenerator::test_build_hierarchical_structure_empty", "tests/test_toc_generator.py::TestTOCGenerator::test_build_hierarchical_structure_flat", "tests/test_toc_generator.py::TestTOCGenerator::test_build_hierarchical_structure_nested", "tests/test_toc_generator.py::TestTOCGenerator::test_generate_summary_stats_empty", "tests/test_toc_generator.py::TestTOCGenerator::test_generate_summary_stats_with_data", "tests/test_toc_generator.py::TestTOCGenerator::test_generate_toc_json_empty", "tests/test_toc_generator.py::TestTOCGenerator::test_generate_toc_json_with_headings", "tests/test_toc_generator.py::TestTOCGenerator::test_generate_toc_markdown_empty", "tests/test_toc_generator.py::TestTOCGenerator::test_generate_toc_markdown_options", "tests/test_toc_generator.py::TestTOCGenerator::test_generate_toc_markdown_with_headings", "tests/test_toc_generator.py::TestTOCGenerator::test_initialization", "tests/test_toc_generator.py::TestTOCGenerator::test_save_toc_json", "tests/test_toc_generator.py::TestTOCGenerator::test_save_toc_markdown", "tests/test_toc_generator.py::TestTOCGenerator::test_validate_toc_structure_empty", "tests/test_toc_generator.py::TestTOCGenerator::test_validate_toc_structure_valid", "tests/test_toc_generator.py::TestTOCGenerator::test_validate_toc_structure_warnings"]