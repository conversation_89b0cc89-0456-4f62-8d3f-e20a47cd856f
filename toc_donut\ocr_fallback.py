"""
OCR Fallback Module

This module provides a fallback OCR method using pytesseract when Donut model
fails to extract readable text from documents.
"""

import re
from pathlib import Path
from typing import List, Optional, Union
from PIL import Image
from loguru import logger

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    logger.warning("pytesseract not available. OCR fallback will be disabled.")


class OCRFallback:
    """
    Fallback OCR processor using Tesseract when Donut fails.
    """
    
    def __init__(self):
        """Initialize the OCR fallback processor."""
        self.available = TESSERACT_AVAILABLE
        if self.available:
            logger.info("OCR fallback initialized with Tesseract")
        else:
            logger.warning("OCR fallback not available - pytesseract not installed")
    
    def extract_text_from_image(self, image_path: Union[str, Path]) -> str:
        """
        Extract text from image using Tesseract OCR.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Extracted text content
        """
        if not self.available:
            logger.warning("OCR fallback not available")
            return ""
        
        try:
            # Load and preprocess image
            image = Image.open(image_path)
            
            # Convert to grayscale for better OCR
            if image.mode != 'L':
                image = image.convert('L')
            
            # Extract text using Tesseract
            text = pytesseract.image_to_string(
                image,
                config='--psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,!?()[]{}:;-_/\\ \n\t'
            )
            
            # Clean the extracted text
            cleaned_text = self._clean_ocr_text(text)
            
            logger.debug(f"OCR extracted {len(cleaned_text)} characters from {image_path}")
            return cleaned_text
            
        except Exception as e:
            logger.error(f"OCR extraction failed: {e}")
            return ""
    
    def _clean_ocr_text(self, text: str) -> str:
        """
        Clean OCR-extracted text.
        
        Args:
            text: Raw OCR text
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove lines with only special characters
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if len(line) < 2:
                continue
            
            # Skip lines that are mostly special characters
            if len(re.sub(r'[^\w\s]', '', line)) < len(line) * 0.5:
                continue
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def is_text_quality_good(self, text: str) -> bool:
        """
        Check if extracted text quality is good enough.
        
        Args:
            text: Extracted text to evaluate
            
        Returns:
            True if text quality is acceptable
        """
        if not text or len(text) < 10:
            return False
        
        # Check ratio of alphabetic characters
        alpha_chars = sum(1 for c in text if c.isalpha())
        total_chars = len(text.replace(' ', '').replace('\n', ''))
        
        if total_chars == 0:
            return False
        
        alpha_ratio = alpha_chars / total_chars
        
        # Good text should have at least 60% alphabetic characters
        return alpha_ratio >= 0.6
    
    def extract_with_fallback(self, image_path: Union[str, Path], donut_text: str) -> str:
        """
        Use OCR as fallback if Donut text quality is poor.
        
        Args:
            image_path: Path to the image file
            donut_text: Text extracted by Donut model
            
        Returns:
            Best available text (Donut or OCR fallback)
        """
        # Check if Donut text is good quality
        if self.is_text_quality_good(donut_text):
            logger.debug("Donut text quality is good, using Donut result")
            return donut_text
        
        # Try OCR fallback
        if self.available:
            logger.info("Donut text quality is poor, trying OCR fallback")
            ocr_text = self.extract_text_from_image(image_path)
            
            if self.is_text_quality_good(ocr_text):
                logger.success("OCR fallback produced good quality text")
                return ocr_text
            else:
                logger.warning("OCR fallback also produced poor quality text")
        
        # Return the best we have
        return donut_text if len(donut_text) > len(ocr_text if 'ocr_text' in locals() else '') else (ocr_text if 'ocr_text' in locals() else donut_text)


def install_tesseract_instructions():
    """Print instructions for installing Tesseract."""
    instructions = """
    To enable OCR fallback, install Tesseract:
    
    Windows:
    1. Download from: https://github.com/UB-Mannheim/tesseract/wiki
    2. Install and add to PATH
    3. pip install pytesseract
    
    macOS:
    brew install tesseract
    pip install pytesseract
    
    Ubuntu/Debian:
    sudo apt-get install tesseract-ocr
    pip install pytesseract
    """
    print(instructions)
