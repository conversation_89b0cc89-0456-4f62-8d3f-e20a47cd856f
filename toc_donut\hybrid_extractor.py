"""
Hybrid Text Extraction Module

This module combines multiple text extraction methods to improve reliability:
1. PyPDF2 for direct PDF text extraction
2. Donut model for image-based extraction
3. OCR fallback for scanned documents
"""

import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from loguru import logger

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    import pytesseract
    from PIL import Image
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False


class HybridTextExtractor:
    """
    Hybrid text extractor that combines multiple extraction methods.
    """
    
    def __init__(self):
        """Initialize the hybrid extractor."""
        self.pypdf2_available = PYPDF2_AVAILABLE
        self.tesseract_available = TESSERACT_AVAILABLE
        
        logger.info(f"Hybrid extractor initialized:")
        logger.info(f"  PyPDF2 available: {self.pypdf2_available}")
        logger.info(f"  Tesseract available: {self.tesseract_available}")
    
    def extract_text_from_pdf(self, pdf_path: Union[str, Path]) -> List[str]:
        """
        Extract text directly from PDF using PyPDF2.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            List of text content for each page
        """
        if not self.pypdf2_available:
            logger.warning("PyPDF2 not available for direct PDF text extraction")
            return []
        
        try:
            pdf_path = Path(pdf_path)
            page_texts = []
            
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    try:
                        text = page.extract_text()
                        cleaned_text = self._clean_pdf_text(text)
                        page_texts.append(cleaned_text)
                        logger.debug(f"Extracted {len(cleaned_text)} chars from page {page_num}")
                    except Exception as e:
                        logger.warning(f"Failed to extract text from page {page_num}: {e}")
                        page_texts.append("")
            
            logger.success(f"Extracted text from {len(page_texts)} pages using PyPDF2")
            return page_texts
            
        except Exception as e:
            logger.error(f"PyPDF2 extraction failed: {e}")
            return []
    
    def extract_text_from_image(self, image_path: Union[str, Path]) -> str:
        """
        Extract text from image using Tesseract OCR.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Extracted text content
        """
        if not self.tesseract_available:
            logger.warning("Tesseract not available for OCR")
            return ""
        
        try:
            image = Image.open(image_path)
            
            # Convert to grayscale for better OCR
            if image.mode != 'L':
                image = image.convert('L')
            
            # Extract text using Tesseract
            text = pytesseract.image_to_string(
                image,
                config='--psm 6'
            )
            
            cleaned_text = self._clean_ocr_text(text)
            logger.debug(f"OCR extracted {len(cleaned_text)} characters")
            return cleaned_text
            
        except Exception as e:
            logger.error(f"OCR extraction failed: {e}")
            return ""
    
    def get_best_text_extraction(
        self, 
        pdf_path: Union[str, Path], 
        image_paths: List[Path], 
        donut_results: List[Dict[str, Any]]
    ) -> List[str]:
        """
        Get the best text extraction by combining multiple methods.
        
        Args:
            pdf_path: Path to the PDF file
            image_paths: List of image file paths
            donut_results: Results from Donut model
            
        Returns:
            List of best text content for each page
        """
        logger.info("Combining text extraction methods for best results")
        
        # Method 1: Try direct PDF text extraction
        pdf_texts = self.extract_text_from_pdf(pdf_path)
        
        # Method 2: Get Donut results
        donut_texts = []
        for result in donut_results:
            if result.get("success", False):
                donut_texts.append(result.get("extracted_text", ""))
            else:
                donut_texts.append("")
        
        # Method 3: OCR fallback for images
        ocr_texts = []
        if self.tesseract_available and image_paths:
            for image_path in image_paths:
                ocr_text = self.extract_text_from_image(image_path)
                ocr_texts.append(ocr_text)
        
        # Combine results - choose the best for each page
        best_texts = []
        max_pages = max(len(pdf_texts), len(donut_texts), len(ocr_texts))
        
        for i in range(max_pages):
            pdf_text = pdf_texts[i] if i < len(pdf_texts) else ""
            donut_text = donut_texts[i] if i < len(donut_texts) else ""
            ocr_text = ocr_texts[i] if i < len(ocr_texts) else ""
            
            # Choose the best text based on quality metrics
            best_text = self._choose_best_text(pdf_text, donut_text, ocr_text)
            best_texts.append(best_text)
            
            logger.debug(f"Page {i+1}: PDF={len(pdf_text)}, Donut={len(donut_text)}, OCR={len(ocr_text)} -> Best={len(best_text)}")
        
        logger.success(f"Combined extraction completed for {len(best_texts)} pages")
        return best_texts
    
    def _choose_best_text(self, pdf_text: str, donut_text: str, ocr_text: str) -> str:
        """
        Choose the best text from multiple extraction methods.
        
        Args:
            pdf_text: Text from PyPDF2
            donut_text: Text from Donut model
            ocr_text: Text from OCR
            
        Returns:
            Best quality text
        """
        texts = [
            ("PDF", pdf_text),
            ("Donut", donut_text),
            ("OCR", ocr_text)
        ]
        
        # Score each text based on quality metrics
        scored_texts = []
        for method, text in texts:
            if not text:
                continue
            
            score = self._calculate_text_quality_score(text)
            scored_texts.append((score, method, text))
        
        if not scored_texts:
            return ""
        
        # Return the highest scoring text
        scored_texts.sort(reverse=True)
        best_score, best_method, best_text = scored_texts[0]
        
        logger.debug(f"Best text from {best_method} (score: {best_score:.3f})")
        return best_text
    
    def _calculate_text_quality_score(self, text: str) -> float:
        """
        Calculate a quality score for extracted text.
        
        Args:
            text: Text to evaluate
            
        Returns:
            Quality score (0.0 to 1.0)
        """
        if not text or len(text) < 5:
            return 0.0
        
        score = 0.0
        
        # Length bonus (up to 0.3)
        length_score = min(len(text) / 1000, 0.3)
        score += length_score
        
        # Alphabetic character ratio (up to 0.4)
        alpha_chars = sum(1 for c in text if c.isalpha())
        total_chars = len(text.replace(' ', '').replace('\n', ''))
        if total_chars > 0:
            alpha_ratio = alpha_chars / total_chars
            score += alpha_ratio * 0.4
        
        # Word count bonus (up to 0.2)
        words = text.split()
        valid_words = [w for w in words if len(w) > 2 and w.isalpha()]
        if len(words) > 0:
            word_ratio = len(valid_words) / len(words)
            score += word_ratio * 0.2
        
        # Penalty for excessive special characters
        special_chars = sum(1 for c in text if not c.isalnum() and c not in ' \n\t.,!?()-')
        if total_chars > 0:
            special_ratio = special_chars / total_chars
            score -= special_ratio * 0.1
        
        return max(0.0, min(1.0, score))
    
    def _clean_pdf_text(self, text: str) -> str:
        """Clean text extracted from PDF."""
        if not text:
            return ""

        # First, try to preserve line breaks for headings
        # Look for patterns that indicate new lines/sections
        text = re.sub(r'(\d+\.\s+[A-Z])', r'\n\1', text)  # "1. TITLE" -> "\n1. TITLE"
        text = re.sub(r'(\d+\.\d+\s+[A-Z])', r'\n\1', text)  # "1.1 Title" -> "\n1.1 Title"
        text = re.sub(r'([A-Z]{3,})', r'\n\1', text)  # "ABSTRACT" -> "\nABSTRAT"
        text = re.sub(r'(REFERENCES|CONCLUSION|INTRODUCTION|METHODOLOGY|RESULTS)', r'\n\1', text)

        # Clean up excessive whitespace but preserve line breaks
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces/tabs to single space
        text = re.sub(r'\n\s*\n', '\n', text)  # Multiple newlines to single
        text = text.strip()

        return text
    
    def _clean_ocr_text(self, text: str) -> str:
        """Clean text extracted from OCR."""
        if not text:
            return ""

        # Split into lines first
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if len(line) < 2:
                continue

            # Skip lines that are mostly special characters
            if len(re.sub(r'[^\w\s]', '', line)) < len(line) * 0.5:
                continue

            # Clean up the line
            line = re.sub(r'\s+', ' ', line)
            cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)
