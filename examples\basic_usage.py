#!/usr/bin/env python3
"""
Basic usage example for toc-donut.

This script demonstrates how to use the toc-donut library programmatically
to extract table of contents from PDF documents.
"""

import sys
from pathlib import Path

# Add the parent directory to the path so we can import toc_donut
sys.path.insert(0, str(Path(__file__).parent.parent))

from toc_donut import PDFProcessor, DonutModel, TextProcessor, TOCGenerator


def main():
    """Main example function."""
    # Configuration
    input_pdf = Path("../input/L0.pdf")  # Adjust path as needed
    output_dir = Path("./output")
    
    print("🔍 toc-donut Basic Usage Example")
    print("=" * 50)
    
    # Check if input file exists
    if not input_pdf.exists():
        print(f"❌ Input PDF not found: {input_pdf}")
        print("Please adjust the input_pdf path in the script.")
        return
    
    try:
        # Initialize components
        print("📄 Initializing PDF processor...")
        pdf_processor = PDFProcessor(dpi=200)
        
        print("🤖 Loading Donut model...")
        donut_model = DonutModel(model_name="naver-clova-ix/donut-base-finetuned-docvqa")
        
        print("🔤 Initializing text processor...")
        text_processor = TextProcessor()
        
        print("📋 Initializing TOC generator...")
        toc_generator = TOCGenerator()
        
        # Get PDF info
        print(f"\n📊 PDF Information:")
        pdf_info = pdf_processor.get_pdf_info(input_pdf)
        for key, value in pdf_info.items():
            if key not in ["error"]:
                print(f"  {key}: {value}")
        
        # Convert PDF to images
        print(f"\n🖼️  Converting PDF to images...")
        image_paths = pdf_processor.convert_pdf_to_images(input_pdf)
        print(f"  Generated {len(image_paths)} images")
        
        # Process pages with Donut
        print(f"\n🔍 Processing pages with Donut model...")
        page_results = donut_model.process_document_pages(
            image_paths,
            extract_headings=True
        )
        
        successful_pages = [r for r in page_results if r.get("success", False)]
        print(f"  Successfully processed {len(successful_pages)}/{len(page_results)} pages")
        
        # Extract headings
        print(f"\n📝 Extracting headings...")
        headings = text_processor.process_pages(page_results)
        print(f"  Found {len(headings)} headings")
        
        if headings:
            print("\n📋 Detected Headings:")
            for i, heading in enumerate(headings[:10], 1):  # Show first 10
                indent = "  " * (heading.level - 1)
                print(f"  {i:2d}. {indent}[L{heading.level}] {heading.text} (Page {heading.page_number}, Conf: {heading.confidence:.2f})")
            
            if len(headings) > 10:
                print(f"  ... and {len(headings) - 10} more headings")
        
        # Generate TOC
        print(f"\n💾 Generating table of contents...")
        output_dir.mkdir(exist_ok=True)
        
        # Generate JSON TOC
        json_path = output_dir / f"{input_pdf.stem}_toc.json"
        toc_generator.save_toc_json(headings, json_path, metadata={
            "source_file": str(input_pdf),
            "example": "basic_usage.py"
        })
        print(f"  ✅ JSON TOC saved to: {json_path}")
        
        # Generate Markdown TOC
        md_path = output_dir / f"{input_pdf.stem}_toc.md"
        toc_generator.save_toc_markdown(headings, md_path, metadata={
            "source_file": str(input_pdf),
            "example": "basic_usage.py"
        })
        print(f"  ✅ Markdown TOC saved to: {md_path}")
        
        # Show summary statistics
        print(f"\n📊 Summary Statistics:")
        stats = toc_generator.generate_summary_stats(headings)
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
        
        # Validate structure
        print(f"\n✅ Structure Validation:")
        validation = toc_generator.validate_toc_structure(headings)
        print(f"  Valid: {validation['valid']}")
        
        if validation["warnings"]:
            print("  Warnings:")
            for warning in validation["warnings"]:
                print(f"    ⚠️  {warning}")
        
        if validation["issues"]:
            print("  Issues:")
            for issue in validation["issues"]:
                print(f"    ❌ {issue}")
        
        print(f"\n🎉 Processing completed successfully!")
        print(f"📁 Output files saved to: {output_dir}")
        
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup temporary files
        try:
            if 'image_paths' in locals():
                print(f"\n🧹 Cleaning up temporary files...")
                pdf_processor.cleanup_temp_files(image_paths)
        except Exception as e:
            print(f"⚠️  Warning: Failed to cleanup temporary files: {e}")


if __name__ == "__main__":
    main()
