"""
toc-donut: Table of Contents Generator using Donut

A production-quality tool for extracting hierarchical table of contents
from PDF documents using the Hugging Face Donut model.
"""

__version__ = "1.0.0"
__author__ = "Donut Project Team"
__email__ = "<EMAIL>"
__description__ = "Table of Contents Generator using Hugging Face Donut model"

from .pdf_processor import PDFProcessor
from .donut_model import DonutModel
from .text_processor import TextProcessor
from .toc_generator import TOCGenerator

__all__ = [
    "PDFProcessor",
    "DonutModel", 
    "TextProcessor",
    "TOCGenerator",
]
