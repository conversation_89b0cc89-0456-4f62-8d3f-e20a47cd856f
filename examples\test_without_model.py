#!/usr/bin/env python3
"""
Test script that demonstrates toc-donut functionality without requiring model download.

This script tests the core components using mock data to show how the system works
without needing to download the large Donut model.
"""

import sys
from pathlib import Path

# Add the parent directory to the path so we can import toc_donut
sys.path.insert(0, str(Path(__file__).parent.parent))

from toc_donut.text_processor import TextProcessor, Heading
from toc_donut.toc_generator import TOCGenerator
from toc_donut.pdf_processor import PDFProcessor


def test_text_processing():
    """Test text processing and heading detection."""
    print("🔤 Testing Text Processing")
    print("=" * 40)
    
    # Sample extracted text that might come from Donut
    sample_text = """
    CHAPTER 1: INTRODUCTION
    
    This chapter provides an overview of the document.
    
    1.1 Background
    
    The background section covers the context.
    
    1.2 Objectives
    
    The main objectives are outlined here.
    
    CHAPTER 2: METHODOLOGY
    
    This chapter describes the methodology used.
    
    2.1 Data Collection
    
    Data was collected using various methods.
    
    2.2 Analysis Techniques
    
    Several analysis techniques were employed.
    """
    
    # Initialize text processor
    text_processor = TextProcessor()
    
    # Extract headings from sample text
    headings = text_processor.extract_headings_from_text(sample_text, page_number=1)
    
    print(f"📝 Extracted {len(headings)} headings:")
    for i, heading in enumerate(headings, 1):
        indent = "  " * (heading.level - 1)
        print(f"  {i}. {indent}[Level {heading.level}] {heading.text}")
        print(f"     Confidence: {heading.confidence:.3f}")
    
    return headings


def test_toc_generation(headings):
    """Test TOC generation in different formats."""
    print("\n📋 Testing TOC Generation")
    print("=" * 40)
    
    # Initialize TOC generator
    toc_generator = TOCGenerator()
    
    # Generate JSON TOC
    toc_json = toc_generator.generate_toc_json(headings, metadata={
        "source_file": "sample_document.pdf",
        "test_mode": True
    })
    
    print("📄 JSON TOC Structure:")
    print(f"  Total headings: {toc_json['metadata']['total_headings']}")
    print(f"  Max level: {toc_json['metadata']['max_level']}")
    print(f"  Hierarchical items: {len(toc_json['table_of_contents'])}")
    
    # Generate Markdown TOC
    toc_markdown = toc_generator.generate_toc_markdown(
        headings, 
        include_page_numbers=True,
        include_confidence=True
    )
    
    print("\n📝 Markdown TOC Preview:")
    lines = toc_markdown.split('\n')
    for line in lines[:15]:  # Show first 15 lines
        print(f"  {line}")
    if len(lines) > 15:
        print(f"  ... ({len(lines) - 15} more lines)")
    
    # Generate summary statistics
    stats = toc_generator.generate_summary_stats(headings)
    print(f"\n📊 Summary Statistics:")
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    
    # Validate structure
    validation = toc_generator.validate_toc_structure(headings)
    print(f"\n✅ Structure Validation:")
    print(f"  Valid: {validation['valid']}")
    if validation["warnings"]:
        print("  Warnings:")
        for warning in validation["warnings"]:
            print(f"    ⚠️  {warning}")
    
    return toc_json, toc_markdown


def test_pdf_info():
    """Test PDF information extraction (without conversion)."""
    print("\n📄 Testing PDF Information")
    print("=" * 40)
    
    # Check for sample PDFs
    input_dir = Path("../input")
    pdf_files = list(input_dir.glob("*.pdf")) if input_dir.exists() else []
    
    if pdf_files:
        pdf_processor = PDFProcessor()
        
        for pdf_file in pdf_files[:2]:  # Test first 2 PDFs
            print(f"\n📋 PDF: {pdf_file.name}")
            try:
                pdf_info = pdf_processor.get_pdf_info(pdf_file)
                for key, value in pdf_info.items():
                    if key not in ["error"] and value:
                        print(f"  {key}: {value}")
            except Exception as e:
                print(f"  ❌ Error: {e}")
    else:
        print("  ⚠️  No PDF files found in input directory")


def test_mock_workflow():
    """Test the complete workflow with mock data."""
    print("\n🔄 Testing Complete Workflow")
    print("=" * 40)
    
    # Create mock page results (as if from Donut model)
    mock_page_results = [
        {
            "page_number": 1,
            "success": True,
            "extracted_text": """
            INTRODUCTION TO MACHINE LEARNING
            
            1. Overview
            Machine learning is a subset of artificial intelligence.
            
            1.1 Supervised Learning
            Supervised learning uses labeled data.
            
            1.2 Unsupervised Learning
            Unsupervised learning finds patterns in unlabeled data.
            """
        },
        {
            "page_number": 2,
            "success": True,
            "extracted_text": """
            2. ALGORITHMS
            
            This section covers various algorithms.
            
            2.1 Linear Regression
            Linear regression is a basic algorithm.
            
            2.2 Decision Trees
            Decision trees are easy to interpret.
            """
        },
        {
            "page_number": 3,
            "success": True,
            "extracted_text": """
            3. EVALUATION METRICS
            
            Model evaluation is crucial.
            
            3.1 Accuracy
            Accuracy measures correct predictions.
            
            3.2 Precision and Recall
            These metrics are important for classification.
            """
        }
    ]
    
    # Process with text processor
    text_processor = TextProcessor()
    headings = text_processor.process_pages(mock_page_results)
    
    print(f"📝 Processed {len(mock_page_results)} pages")
    print(f"📋 Found {len(headings)} headings")
    
    # Generate TOC
    toc_generator = TOCGenerator()
    
    # Save to files
    output_dir = Path("./test_output")
    output_dir.mkdir(exist_ok=True)
    
    json_path = output_dir / "mock_toc.json"
    md_path = output_dir / "mock_toc.md"
    
    toc_generator.save_toc_json(headings, json_path, metadata={
        "source": "mock_workflow_test",
        "pages_processed": len(mock_page_results)
    })
    
    toc_generator.save_toc_markdown(headings, md_path, metadata={
        "source": "mock_workflow_test",
        "pages_processed": len(mock_page_results)
    })
    
    print(f"💾 Saved JSON TOC to: {json_path}")
    print(f"💾 Saved Markdown TOC to: {md_path}")
    
    return headings


def main():
    """Run all tests."""
    print("🧪 toc-donut Component Testing (No Model Required)")
    print("=" * 60)
    
    try:
        # Test 1: Text processing
        headings = test_text_processing()
        
        # Test 2: TOC generation
        toc_json, toc_markdown = test_toc_generation(headings)
        
        # Test 3: PDF info (if PDFs available)
        test_pdf_info()
        
        # Test 4: Complete workflow with mock data
        mock_headings = test_mock_workflow()
        
        print(f"\n🎉 All tests completed successfully!")
        print(f"📁 Check the test_output directory for generated files")
        
        # Summary
        print(f"\n📊 Test Summary:")
        print(f"  Text processing: ✅ {len(headings)} headings extracted")
        print(f"  TOC generation: ✅ JSON and Markdown formats")
        print(f"  Mock workflow: ✅ {len(mock_headings)} headings from mock data")
        print(f"  Output files: ✅ Saved to test_output/")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
