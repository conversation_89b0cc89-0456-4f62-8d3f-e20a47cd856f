#!/usr/bin/env python3
"""
Advanced usage example for toc-donut.

This script demonstrates advanced features like custom model configuration,
batch processing, and detailed analysis of extraction results.
"""

import sys
from pathlib import Path
import json

# Add the parent directory to the path so we can import toc_donut
sys.path.insert(0, str(Path(__file__).parent.parent))

from toc_donut import PDFProcessor, DonutModel, TextProcessor, TOCGenerator


def process_multiple_pdfs(input_dir: Path, output_dir: Path):
    """Process multiple PDF files in a directory."""
    print("📁 Batch Processing Multiple PDFs")
    print("=" * 50)
    
    pdf_files = list(input_dir.glob("*.pdf"))
    if not pdf_files:
        print(f"❌ No PDF files found in {input_dir}")
        return
    
    print(f"Found {len(pdf_files)} PDF files to process")
    
    # Initialize components once for efficiency
    pdf_processor = PDFProcessor(dpi=150)  # Lower DPI for faster processing
    donut_model = DonutModel()
    text_processor = TextProcessor()
    toc_generator = TOCGenerator()
    
    results = []
    
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"\n📄 Processing {i}/{len(pdf_files)}: {pdf_file.name}")
        
        try:
            # Process the PDF
            image_paths = pdf_processor.convert_pdf_to_images(pdf_file)
            page_results = donut_model.process_document_pages(image_paths, extract_headings=True)
            headings = text_processor.process_pages(page_results)
            
            # Generate outputs
            output_base = output_dir / pdf_file.stem
            toc_generator.save_toc_json(headings, output_base.with_suffix('.json'))
            toc_generator.save_toc_markdown(headings, output_base.with_suffix('.md'))
            
            # Collect results
            stats = toc_generator.generate_summary_stats(headings)
            results.append({
                "file": pdf_file.name,
                "headings_count": len(headings),
                "stats": stats,
                "success": True
            })
            
            print(f"  ✅ Found {len(headings)} headings")
            
            # Cleanup
            pdf_processor.cleanup_temp_files(image_paths)
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            results.append({
                "file": pdf_file.name,
                "error": str(e),
                "success": False
            })
    
    # Save batch results summary
    summary_file = output_dir / "batch_processing_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 Batch processing completed!")
    print(f"📁 Results saved to: {output_dir}")
    print(f"📋 Summary saved to: {summary_file}")


def analyze_document_structure(pdf_path: Path):
    """Perform detailed analysis of document structure."""
    print("🔍 Document Structure Analysis")
    print("=" * 50)
    
    # Initialize components
    pdf_processor = PDFProcessor()
    donut_model = DonutModel()
    text_processor = TextProcessor()
    toc_generator = TOCGenerator()
    
    # Get PDF information
    pdf_info = pdf_processor.get_pdf_info(pdf_path)
    print(f"📄 Document: {pdf_path.name}")
    print(f"📊 Pages: {pdf_info.get('page_count', 'Unknown')}")
    print(f"💾 Size: {pdf_info.get('file_size', 0) / 1024 / 1024:.1f} MB")
    
    # Process document
    image_paths = pdf_processor.convert_pdf_to_images(pdf_path)
    page_results = donut_model.process_document_pages(image_paths, extract_headings=True)
    headings = text_processor.process_pages(page_results)
    
    # Detailed analysis
    print(f"\n📋 Heading Analysis:")
    print(f"  Total headings: {len(headings)}")
    
    if headings:
        # Analyze by level
        level_counts = {}
        for heading in headings:
            level_counts[heading.level] = level_counts.get(heading.level, 0) + 1
        
        print(f"  Heading levels:")
        for level in sorted(level_counts.keys()):
            print(f"    Level {level}: {level_counts[level]} headings")
        
        # Analyze confidence distribution
        confidences = [h.confidence for h in headings]
        avg_confidence = sum(confidences) / len(confidences)
        min_confidence = min(confidences)
        max_confidence = max(confidences)
        
        print(f"  Confidence scores:")
        print(f"    Average: {avg_confidence:.3f}")
        print(f"    Range: {min_confidence:.3f} - {max_confidence:.3f}")
        
        # Show low confidence headings
        low_confidence = [h for h in headings if h.confidence < 0.5]
        if low_confidence:
            print(f"  ⚠️  Low confidence headings ({len(low_confidence)}):")
            for heading in low_confidence[:5]:  # Show first 5
                print(f"    '{heading.text}' (Page {heading.page_number}, Conf: {heading.confidence:.3f})")
        
        # Validate structure
        validation = toc_generator.validate_toc_structure(headings)
        print(f"\n✅ Structure Validation:")
        print(f"  Valid: {validation['valid']}")
        
        if validation["warnings"]:
            print(f"  Warnings:")
            for warning in validation["warnings"]:
                print(f"    ⚠️  {warning}")
        
        # Show sample headings
        print(f"\n📝 Sample Headings:")
        for i, heading in enumerate(headings[:10], 1):
            indent = "  " * heading.level
            print(f"  {i:2d}. {indent}[L{heading.level}] {heading.text}")
            print(f"      (Page {heading.page_number}, Confidence: {heading.confidence:.3f})")
    
    # Cleanup
    pdf_processor.cleanup_temp_files(image_paths)


def custom_model_example():
    """Example of using custom model configurations."""
    print("🤖 Custom Model Configuration Example")
    print("=" * 50)
    
    # Example with different model
    try:
        # You can use different Donut models
        model_configs = [
            {
                "name": "naver-clova-ix/donut-base-finetuned-docvqa",
                "description": "Document VQA model (good for general text extraction)"
            },
            {
                "name": "naver-clova-ix/donut-base-finetuned-cord-v2", 
                "description": "Receipt understanding model"
            },
            {
                "name": "naver-clova-ix/donut-base",
                "description": "Base model (requires task-specific prompts)"
            }
        ]
        
        print("Available model configurations:")
        for i, config in enumerate(model_configs, 1):
            print(f"  {i}. {config['name']}")
            print(f"     {config['description']}")
        
        # Example of custom task prompts
        custom_prompts = {
            "heading_extraction": "<s_docvqa><s_question>What are the main headings and section titles in this document?</s_question><s_answer>",
            "general_text": "<s_docvqa><s_question>What is the text content of this document?</s_question><s_answer>",
            "structured_parsing": "<s_parsing>",
        }
        
        print(f"\nCustom task prompts:")
        for task, prompt in custom_prompts.items():
            print(f"  {task}: {prompt}")
        
    except Exception as e:
        print(f"❌ Error in model configuration: {e}")


def main():
    """Main function to run examples."""
    print("🚀 toc-donut Advanced Usage Examples")
    print("=" * 60)
    
    # Setup paths
    input_dir = Path("../input")
    output_dir = Path("./advanced_output")
    output_dir.mkdir(exist_ok=True)
    
    # Example 1: Custom model configuration
    custom_model_example()
    print("\n" + "="*60 + "\n")
    
    # Example 2: Document structure analysis
    sample_pdf = input_dir / "L0.pdf"
    if sample_pdf.exists():
        try:
            analyze_document_structure(sample_pdf)
        except Exception as e:
            print(f"❌ Error in structure analysis: {e}")
    else:
        print(f"⚠️  Sample PDF not found: {sample_pdf}")
    
    print("\n" + "="*60 + "\n")
    
    # Example 3: Batch processing
    if input_dir.exists():
        try:
            process_multiple_pdfs(input_dir, output_dir)
        except Exception as e:
            print(f"❌ Error in batch processing: {e}")
    else:
        print(f"⚠️  Input directory not found: {input_dir}")
    
    print(f"\n🎉 Advanced examples completed!")
    print(f"📁 Check the output directory: {output_dir}")


if __name__ == "__main__":
    main()
