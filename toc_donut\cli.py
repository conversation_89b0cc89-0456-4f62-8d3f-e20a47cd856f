"""
Command-Line Interface for toc-donut

This module provides the main CLI interface for the Table of Contents Generator.
"""

import sys
import tempfile
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.table import Table
from rich.panel import Panel
from loguru import logger

from .pdf_processor import PDFProcessor
from .donut_model import DonutModel
from .text_processor import TextProcessor
from .toc_generator import TOCGenerator


console = Console()


def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    logger.remove()  # Remove default handler
    
    if verbose:
        logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="DEBUG"
        )
    else:
        logger.add(
            sys.stderr,
            format="<level>{level: <8}</level> | <level>{message}</level>",
            level="INFO"
        )


@click.command()
@click.argument('input_pdf', type=click.Path(exists=True, path_type=Path))
@click.option(
    '--output', '-o',
    type=click.Path(path_type=Path),
    help='Output file path (extension determines format)'
)
@click.option(
    '--output-dir', '-d',
    type=click.Path(path_type=Path),
    help='Output directory for multiple formats'
)
@click.option(
    '--format', '-f',
    type=click.Choice(['json', 'markdown', 'both'], case_sensitive=False),
    default='json',
    help='Output format [default: json]'
)
@click.option(
    '--model',
    default='naver-clova-ix/donut-base-finetuned-docvqa',
    help='Hugging Face model name [default: naver-clova-ix/donut-base-finetuned-docvqa]'
)
@click.option(
    '--dpi',
    type=int,
    default=200,
    help='DPI for PDF to image conversion [default: 200]'
)
@click.option(
    '--page-range',
    type=str,
    help='Page range to process (e.g., "1-10" or "5")'
)
@click.option(
    '--include-page-numbers/--no-page-numbers',
    default=True,
    help='Include page numbers in Markdown output [default: True]'
)
@click.option(
    '--include-confidence/--no-confidence',
    default=False,
    help='Include confidence scores in output [default: False]'
)
@click.option(
    '--verbose', '-v',
    is_flag=True,
    help='Enable verbose logging'
)
@click.option(
    '--cleanup/--no-cleanup',
    default=True,
    help='Clean up temporary files [default: True]'
)
def main(
    input_pdf: Path,
    output: Optional[Path],
    output_dir: Optional[Path],
    format: str,
    model: str,
    dpi: int,
    page_range: Optional[str],
    include_page_numbers: bool,
    include_confidence: bool,
    verbose: bool,
    cleanup: bool
):
    """
    Generate Table of Contents from PDF using Donut model.
    
    INPUT_PDF: Path to the PDF file to process
    """
    setup_logging(verbose)
    
    try:
        # Validate arguments
        if not output and not output_dir:
            output = input_pdf.with_suffix('.json')
            console.print(f"[yellow]No output specified, using: {output}[/yellow]")
        
        if output and output_dir:
            console.print("[red]Error: Cannot specify both --output and --output-dir[/red]")
            sys.exit(1)
        
        # Parse page range
        page_range_tuple = None
        if page_range:
            page_range_tuple = parse_page_range(page_range)
        
        # Display startup information
        display_startup_info(input_pdf, model, dpi, page_range_tuple)
        
        # Initialize components
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Initialize PDF processor
            init_task = progress.add_task("Initializing PDF processor...", total=None)
            pdf_processor = PDFProcessor(dpi=dpi)
            progress.update(init_task, completed=True)
            
            # Initialize Donut model
            model_task = progress.add_task("Loading Donut model...", total=None)
            donut_model = DonutModel(model_name=model)
            progress.update(model_task, completed=True)
            
            # Initialize text processor and TOC generator
            text_processor = TextProcessor()
            toc_generator = TOCGenerator()
        
        # Process the PDF
        temp_image_paths = []
        try:
            # Convert PDF to images
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
                TimeElapsedColumn(),
                console=console
            ) as progress:
                
                convert_task = progress.add_task("Converting PDF to images...", total=None)
                temp_image_paths = pdf_processor.convert_pdf_to_images(
                    input_pdf, 
                    page_range=page_range_tuple
                )
                progress.update(convert_task, completed=True)
                
                # Process pages with Donut model
                process_task = progress.add_task(
                    "Processing pages with Donut model...", 
                    total=len(temp_image_paths)
                )
                
                def progress_callback(current, total):
                    progress.update(process_task, completed=current)
                
                page_results = donut_model.process_document_pages(
                    temp_image_paths,
                    extract_headings=True,
                    progress_callback=progress_callback
                )
                
                # Extract headings
                extract_task = progress.add_task("Extracting headings...", total=None)
                headings = text_processor.process_pages(page_results)
                progress.update(extract_task, completed=True)
            
            # Display results summary
            display_results_summary(headings, toc_generator)
            
            # Generate and save output
            generate_output(
                headings, 
                toc_generator, 
                format, 
                output, 
                output_dir, 
                input_pdf,
                include_page_numbers,
                include_confidence
            )
            
        finally:
            # Cleanup temporary files
            if cleanup and temp_image_paths:
                with console.status("Cleaning up temporary files..."):
                    pdf_processor.cleanup_temp_files(temp_image_paths)
        
        console.print("\n[green]✓ Table of Contents generation completed successfully![/green]")
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        console.print(f"\n[red]✗ Error: {e}[/red]")
        sys.exit(1)


def parse_page_range(page_range_str: str) -> tuple:
    """Parse page range string into tuple."""
    try:
        if '-' in page_range_str:
            start, end = page_range_str.split('-', 1)
            return (int(start.strip()), int(end.strip()))
        else:
            page = int(page_range_str.strip())
            return (page, page)
    except ValueError:
        raise ValueError(f"Invalid page range format: {page_range_str}")


def display_startup_info(input_pdf: Path, model: str, dpi: int, page_range: Optional[tuple]):
    """Display startup information."""
    info_table = Table(title="Processing Configuration", show_header=False)
    info_table.add_column("Setting", style="cyan")
    info_table.add_column("Value", style="white")
    
    info_table.add_row("Input PDF", str(input_pdf))
    info_table.add_row("Model", model)
    info_table.add_row("DPI", str(dpi))
    
    if page_range:
        if page_range[0] == page_range[1]:
            info_table.add_row("Page Range", f"Page {page_range[0]}")
        else:
            info_table.add_row("Page Range", f"Pages {page_range[0]}-{page_range[1]}")
    else:
        info_table.add_row("Page Range", "All pages")
    
    console.print(info_table)
    console.print()


def display_results_summary(headings, toc_generator):
    """Display summary of extraction results."""
    if not headings:
        console.print(Panel(
            "[yellow]No headings were detected in the document.[/yellow]",
            title="Results Summary",
            border_style="yellow"
        ))
        return
    
    stats = toc_generator.generate_summary_stats(headings)
    validation = toc_generator.validate_toc_structure(headings)
    
    # Create summary table
    summary_table = Table(title="Extraction Results", show_header=False)
    summary_table.add_column("Metric", style="cyan")
    summary_table.add_column("Value", style="white")
    
    summary_table.add_row("Total Headings", str(stats["total_headings"]))
    summary_table.add_row("Heading Levels", f"{stats['min_level']}-{stats['max_level']}")
    summary_table.add_row("Page Range", f"{stats['page_range']['first']}-{stats['page_range']['last']}")
    summary_table.add_row("Avg Confidence", f"{stats['avg_confidence']:.2f}")
    
    console.print(summary_table)
    
    # Display warnings if any
    if validation["warnings"]:
        console.print("\n[yellow]Warnings:[/yellow]")
        for warning in validation["warnings"]:
            console.print(f"  • {warning}")
    
    console.print()


def generate_output(
    headings, 
    toc_generator, 
    format_type: str, 
    output: Optional[Path], 
    output_dir: Optional[Path],
    input_pdf: Path,
    include_page_numbers: bool,
    include_confidence: bool
):
    """Generate and save output files."""
    # Prepare metadata
    metadata = {
        "source_file": str(input_pdf),
        "source_filename": input_pdf.name
    }
    
    if output_dir:
        output_dir.mkdir(parents=True, exist_ok=True)
        base_name = input_pdf.stem
        
        if format_type in ['json', 'both']:
            json_path = output_dir / f"{base_name}_toc.json"
            toc_generator.save_toc_json(headings, json_path, metadata)
            console.print(f"[green]✓ JSON TOC saved to: {json_path}[/green]")
        
        if format_type in ['markdown', 'both']:
            md_path = output_dir / f"{base_name}_toc.md"
            toc_generator.save_toc_markdown(
                headings, md_path, metadata, include_page_numbers, include_confidence
            )
            console.print(f"[green]✓ Markdown TOC saved to: {md_path}[/green]")
    
    elif output:
        if format_type == 'json' or output.suffix.lower() == '.json':
            toc_generator.save_toc_json(headings, output, metadata)
            console.print(f"[green]✓ JSON TOC saved to: {output}[/green]")
        elif format_type == 'markdown' or output.suffix.lower() in ['.md', '.markdown']:
            toc_generator.save_toc_markdown(
                headings, output, metadata, include_page_numbers, include_confidence
            )
            console.print(f"[green]✓ Markdown TOC saved to: {output}[/green]")
        else:
            # Default to JSON
            toc_generator.save_toc_json(headings, output, metadata)
            console.print(f"[green]✓ JSON TOC saved to: {output}[/green]")


if __name__ == '__main__':
    main()
