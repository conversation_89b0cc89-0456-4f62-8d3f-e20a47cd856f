"""
Tests for text processing and hierarchy detection.
"""

import pytest
from toc_donut.text_processor import TextProcessor, Heading


class TestTextProcessor:
    """Test cases for TextProcessor class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.processor = TextProcessor()
    
    def test_initialization(self):
        """Test TextProcessor initialization."""
        assert self.processor is not None
        assert len(self.processor.heading_patterns) > 0
        assert len(self.processor.noise_patterns) > 0
    
    def test_extract_headings_from_text_simple(self):
        """Test basic heading extraction."""
        text = """
        1. Introduction
        This is some body text.
        
        2. Methodology
        More body text here.
        
        2.1 Data Collection
        Subsection content.
        """
        
        headings = self.processor.extract_headings_from_text(text, page_number=1)
        
        assert len(headings) >= 2
        heading_texts = [h.text for h in headings]
        assert "Introduction" in heading_texts
        assert "Methodology" in heading_texts
    
    def test_extract_headings_with_chapters(self):
        """Test extraction of chapter-style headings."""
        text = """
        CHAPTER 1: Getting Started
        
        This chapter covers the basics.
        
        Chapter 2: Advanced Topics
        
        More advanced material here.
        """
        
        headings = self.processor.extract_headings_from_text(text, page_number=1)
        
        assert len(headings) >= 2
        chapter_headings = [h for h in headings if "Getting Started" in h.text or "Advanced Topics" in h.text]
        assert len(chapter_headings) >= 2
    
    def test_noise_filtering(self):
        """Test that noise is properly filtered out."""
        noisy_lines = [
            "",  # Empty line
            "page 5",  # Page number
            "123",  # Standalone number
            "see section 2.1",  # Reference
            "figure 1.2: diagram",  # Figure caption
            "a",  # Very short
        ]
        
        for line in noisy_lines:
            assert self.processor._is_noise(line), f"Line should be considered noise: '{line}'"
    
    def test_confidence_calculation(self):
        """Test confidence score calculation."""
        # Good heading
        confidence1 = self.processor._calculate_confidence(
            "Introduction to Machine Learning", 
            "1. Introduction to Machine Learning", 
            0.9, 
            1
        )
        
        # Very short heading (should have lower confidence)
        confidence2 = self.processor._calculate_confidence(
            "A", 
            "1. A", 
            0.9, 
            1
        )
        
        # Very long heading (should have lower confidence)
        confidence3 = self.processor._calculate_confidence(
            "This is an extremely long heading that goes on and on and probably should not be considered a real heading", 
            "1. This is an extremely long heading that goes on and on and probably should not be considered a real heading", 
            0.9, 
            1
        )
        
        assert confidence1 > confidence2
        assert confidence1 > confidence3
    
    def test_heading_level_adjustment(self):
        """Test heading level adjustment."""
        headings = [
            Heading("Title", 3, 1, 0.9),
            Heading("Subtitle", 4, 1, 0.8),
            Heading("Section", 5, 2, 0.7),
        ]
        
        adjusted = self.processor._adjust_heading_levels(headings)
        
        # Levels should be normalized to start from 1
        levels = [h.level for h in adjusted]
        assert min(levels) == 1
        assert max(levels) == 3
    
    def test_process_pages_empty(self):
        """Test processing empty page results."""
        page_results = []
        headings = self.processor.process_pages(page_results)
        assert len(headings) == 0
    
    def test_process_pages_failed(self):
        """Test processing pages with failures."""
        page_results = [
            {"page_number": 1, "success": False, "error": "Processing failed"},
            {"page_number": 2, "success": True, "extracted_text": "1. Valid Heading"}
        ]
        
        headings = self.processor.process_pages(page_results)
        assert len(headings) >= 0  # Should handle failed pages gracefully
    
    def test_duplicate_removal(self):
        """Test removal of duplicate headings."""
        headings = [
            Heading("Introduction", 1, 1, 0.9),
            Heading("Introduction", 1, 2, 0.8),  # Duplicate
            Heading("Methodology", 1, 3, 0.9),
        ]
        
        refined = self.processor._refine_headings(headings)
        
        # Should remove duplicates
        texts = [h.text for h in refined]
        assert len(set(texts)) == len(texts)  # All texts should be unique
    
    def test_low_confidence_filtering(self):
        """Test filtering of low confidence headings."""
        headings = [
            Heading("Good Heading", 1, 1, 0.8),
            Heading("Bad Heading", 1, 1, 0.1),  # Very low confidence
            Heading("Another Good Heading", 1, 1, 0.7),
        ]
        
        refined = self.processor._refine_headings(headings)
        
        # Should filter out very low confidence headings
        confidences = [h.confidence for h in refined]
        assert all(c >= 0.3 for c in confidences)


class TestHeading:
    """Test cases for Heading dataclass."""
    
    def test_heading_creation(self):
        """Test Heading object creation."""
        heading = Heading("Test Heading", 1, 1, 0.9)
        
        assert heading.text == "Test Heading"
        assert heading.level == 1
        assert heading.page_number == 1
        assert heading.confidence == 0.9
        assert heading.original_text == "Test Heading"  # Should default to text
    
    def test_heading_with_original_text(self):
        """Test Heading with explicit original text."""
        heading = Heading("Clean Text", 1, 1, 0.9, "1. Clean Text")
        
        assert heading.text == "Clean Text"
        assert heading.original_text == "1. Clean Text"


if __name__ == "__main__":
    pytest.main([__file__])
