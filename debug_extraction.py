#!/usr/bin/env python3
"""
Debug script to see what text is actually being extracted by the Donut model.
"""

import sys
from pathlib import Path
from toc_donut import PDFProcessor, DonutModel, TextProcessor

def debug_text_extraction():
    """Debug the text extraction process."""
    print("🔍 Debugging Text Extraction")
    print("=" * 60)
    
    # Initialize components
    pdf_processor = PDFProcessor(dpi=300)
    donut_model = DonutModel()
    text_processor = TextProcessor()
    
    # Test with our created PDF
    pdf_path = Path("input/test_document.pdf")
    
    if not pdf_path.exists():
        print(f"❌ Test PDF not found: {pdf_path}")
        return
    
    print(f"📄 Processing: {pdf_path}")
    
    try:
        # Convert PDF to images
        print("\n🖼️  Converting PDF to images...")
        image_paths = pdf_processor.convert_pdf_to_images(pdf_path, page_range=(1, 2))
        print(f"Generated {len(image_paths)} images")
        
        # Process each page and show extracted text
        for i, image_path in enumerate(image_paths, 1):
            print(f"\n📄 Page {i}: {image_path}")
            print("-" * 40)
            
            # Extract text with different prompts
            prompts = [
                "<s_docvqa><s_question>Extract all text from this document page, including headings, titles, and section names</s_question><s_answer>",
                "<s_docvqa><s_question>What is the text content of this document?</s_question><s_answer>",
                "<s_docvqa><s_question>List all headings and titles in this document</s_question><s_answer>"
            ]
            
            for j, prompt in enumerate(prompts, 1):
                print(f"\n🔤 Prompt {j}: {prompt[:50]}...")
                extracted_text = donut_model.extract_text_from_image(image_path, task_prompt=prompt)
                
                print(f"📝 Extracted text ({len(extracted_text)} chars):")
                if extracted_text:
                    print(f"'{extracted_text[:200]}{'...' if len(extracted_text) > 200 else ''}'")
                    
                    # Try to extract headings from this text
                    headings = text_processor.extract_headings_from_text(extracted_text, i)
                    print(f"🎯 Found {len(headings)} headings:")
                    for heading in headings:
                        print(f"  - '{heading.text}' (Level {heading.level}, Conf: {heading.confidence:.3f})")
                else:
                    print("  (No text extracted)")
                print()
        
        # Cleanup
        pdf_processor.cleanup_temp_files(image_paths)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_simple_text_processing():
    """Test text processing with known good text."""
    print("\n🧪 Testing Text Processing with Known Text")
    print("=" * 60)
    
    # Test with the exact text that should be in our PDF
    test_text = """MACHINE LEARNING RESEARCH PAPER

ABSTRACT

This paper presents a comprehensive study of machine learning algorithms.

1. INTRODUCTION

Machine learning has revolutionized data processing.

1.1 Background

The background of machine learning dates back to the 1950s.

2. METHODOLOGY

This section describes our research methodology.

2.1 Data Collection

Data was collected from multiple sources.

3. RESULTS

Our experiments demonstrate significant improvements.

4. CONCLUSION

Our research demonstrates the effectiveness of the approach.

REFERENCES

[1] Smith, J. (2020). Machine Learning Fundamentals."""
    
    text_processor = TextProcessor()
    headings = text_processor.extract_headings_from_text(test_text, 1)
    
    print(f"📝 Input text ({len(test_text)} chars)")
    print(f"🎯 Found {len(headings)} headings:")
    
    for heading in headings:
        indent = "  " * (heading.level - 1)
        print(f"  {indent}[L{heading.level}] '{heading.text}' (Conf: {heading.confidence:.3f})")
    
    if len(headings) == 0:
        print("❌ No headings found! There might be an issue with the text processing patterns.")
    else:
        print("✅ Text processing is working correctly!")

def main():
    """Run debugging tests."""
    print("🐛 toc-donut Debugging Session")
    print("=" * 80)
    
    # Test 1: Simple text processing
    test_simple_text_processing()
    
    # Test 2: Full extraction debugging
    debug_text_extraction()
    
    print("\n🎯 Debugging completed!")

if __name__ == "__main__":
    main()
