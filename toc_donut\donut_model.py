"""
Donut Model Integration Module

This module handles the loading and inference of the Hugging Face Donut model
for document understanding and text extraction from document images.
"""

import re
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import torch
from PIL import Image
from transformers import DonutProcessor, VisionEncoderDecoderModel
from loguru import logger

try:
    from .ocr_fallback import OCRFallback
    OCR_FALLBACK_AVAILABLE = True
except ImportError:
    OCR_FALLBACK_AVAILABLE = False


class DonutModel:
    """
    Wrapper for Hugging Face Donut model for document understanding.
    """
    
    def __init__(
        self,
        model_name: str = "naver-clova-ix/donut-base-finetuned-docvqa",
        device: Optional[str] = None,
        cache_dir: Optional[str] = None
    ):
        """
        Initialize the Donut model.
        
        Args:
            model_name: Hugging Face model name/path
            device: Device to run model on (auto-detected if None)
            cache_dir: Directory to cache model files
        """
        self.model_name = model_name
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.cache_dir = cache_dir
        
        logger.info(f"Initializing Donut model: {model_name}")
        logger.info(f"Using device: {self.device}")
        
        self.processor = None
        self.model = None
        self.ocr_fallback = OCRFallback() if OCR_FALLBACK_AVAILABLE else None
        self._load_model()
    
    def _load_model(self) -> None:
        """Load the Donut model and processor."""
        try:
            logger.info("Loading Donut processor...")
            self.processor = DonutProcessor.from_pretrained(
                self.model_name,
                cache_dir=self.cache_dir
            )
            
            logger.info("Loading Donut model...")
            self.model = VisionEncoderDecoderModel.from_pretrained(
                self.model_name,
                cache_dir=self.cache_dir
            )
            
            # Move model to device
            self.model.to(self.device)
            self.model.eval()
            
            logger.success(f"Donut model loaded successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load Donut model: {e}")
            raise RuntimeError(f"Model loading failed: {e}")
    
    def extract_text_from_image(
        self,
        image: Union[str, Path, Image.Image],
        task_prompt: str = "<s_docvqa><s_question>What is the text content of this document?</s_question><s_answer>",
        max_length: int = 1024,
        num_beams: int = 3,
        temperature: float = 1.0
    ) -> str:
        """
        Extract text from a document image using Donut.
        
        Args:
            image: PIL Image, path to image file, or image data
            task_prompt: Task-specific prompt for the model
            max_length: Maximum length of generated text
            num_beams: Number of beams for beam search
            temperature: Sampling temperature
            
        Returns:
            Extracted text content
        """
        # Load image if path is provided
        if isinstance(image, (str, Path)):
            image = Image.open(image)
        elif not isinstance(image, Image.Image):
            raise ValueError("Image must be PIL Image or path to image file")
        
        # Ensure image is in RGB mode
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        try:
            # Prepare inputs
            pixel_values = self.processor(image, return_tensors="pt").pixel_values
            pixel_values = pixel_values.to(self.device)
            
            # Prepare decoder inputs
            decoder_input_ids = self.processor.tokenizer(
                task_prompt,
                add_special_tokens=False,
                return_tensors="pt"
            ).input_ids
            decoder_input_ids = decoder_input_ids.to(self.device)
            
            # Generate text
            with torch.no_grad():
                outputs = self.model.generate(
                    pixel_values,
                    decoder_input_ids=decoder_input_ids,
                    max_length=max_length,
                    num_beams=num_beams,
                    temperature=temperature,
                    early_stopping=True,
                    pad_token_id=self.processor.tokenizer.pad_token_id,
                    eos_token_id=self.processor.tokenizer.eos_token_id,
                    use_cache=True,
                    bad_words_ids=[[self.processor.tokenizer.unk_token_id]],
                    return_dict_in_generate=True,
                )
            
            # Decode the generated text
            sequence = outputs.sequences[0]
            decoded_text = self.processor.batch_decode([sequence], skip_special_tokens=True)[0]
            
            # Clean up the text
            cleaned_text = self._clean_extracted_text(decoded_text, task_prompt)

            logger.debug(f"Raw extracted text: {decoded_text[:200]}...")
            logger.debug(f"Cleaned text length: {len(cleaned_text)} characters")
            logger.debug(f"Cleaned text preview: {cleaned_text[:200]}...")

            # Try OCR fallback if text quality is poor and fallback is available
            if self.ocr_fallback and hasattr(self.ocr_fallback, 'extract_with_fallback'):
                final_text = self.ocr_fallback.extract_with_fallback(image, cleaned_text)
                if final_text != cleaned_text:
                    logger.info("Used OCR fallback for better text quality")
                return final_text

            return cleaned_text
            
        except Exception as e:
            logger.error(f"Text extraction failed: {e}")
            return ""
    
    def extract_structured_content(
        self,
        image: Union[str, Path, Image.Image],
        task_prompt: str = "<s_parsing>",
        max_length: int = 1024
    ) -> Dict[str, Any]:
        """
        Extract structured content from document image.
        
        Args:
            image: PIL Image, path to image file, or image data
            task_prompt: Task-specific prompt for structured parsing
            max_length: Maximum length of generated text
            
        Returns:
            Dictionary with structured content
        """
        try:
            # Extract raw text
            raw_text = self.extract_text_from_image(
                image, 
                task_prompt=task_prompt,
                max_length=max_length
            )
            
            # Try to parse as JSON if it looks like structured data
            if raw_text.strip().startswith('{') and raw_text.strip().endswith('}'):
                try:
                    return json.loads(raw_text)
                except json.JSONDecodeError:
                    pass
            
            # Return as plain text if not structured
            return {"text": raw_text}
            
        except Exception as e:
            logger.error(f"Structured content extraction failed: {e}")
            return {"error": str(e)}
    
    def process_document_pages(
        self,
        image_paths: List[Path],
        extract_headings: bool = True,
        progress_callback: Optional[callable] = None
    ) -> List[Dict[str, Any]]:
        """
        Process multiple document pages and extract content.
        
        Args:
            image_paths: List of paths to image files
            extract_headings: Whether to focus on heading extraction
            progress_callback: Optional callback for progress updates
            
        Returns:
            List of dictionaries with page content
        """
        results = []
        
        # Define task prompt based on requirements - use simpler prompts
        if extract_headings:
            task_prompt = "<s_docvqa><s_question>What is the text content of this document?</s_question><s_answer>"
        else:
            task_prompt = "<s_docvqa><s_question>What is the text content of this document?</s_question><s_answer>"
        
        logger.info(f"Processing {len(image_paths)} document pages")
        
        for i, image_path in enumerate(image_paths):
            try:
                logger.debug(f"Processing page {i+1}/{len(image_paths)}: {image_path}")
                
                # Extract text from page
                extracted_text = self.extract_text_from_image(
                    image_path,
                    task_prompt=task_prompt
                )
                
                page_result = {
                    "page_number": i + 1,
                    "image_path": str(image_path),
                    "extracted_text": extracted_text,
                    "success": True
                }
                
                results.append(page_result)
                
                # Call progress callback if provided
                if progress_callback:
                    progress_callback(i + 1, len(image_paths))
                    
            except Exception as e:
                logger.error(f"Failed to process page {i+1}: {e}")
                page_result = {
                    "page_number": i + 1,
                    "image_path": str(image_path),
                    "extracted_text": "",
                    "success": False,
                    "error": str(e)
                }
                results.append(page_result)
        
        logger.success(f"Processed {len(results)} pages")
        return results
    
    def _clean_extracted_text(self, text: str, task_prompt: str) -> str:
        """
        Clean and post-process extracted text.

        Args:
            text: Raw extracted text
            task_prompt: The task prompt used

        Returns:
            Cleaned text
        """
        # Remove the task prompt from the beginning if present
        if text.startswith(task_prompt):
            text = text[len(task_prompt):].strip()

        # Remove common artifacts
        text = re.sub(r'</s_answer>.*$', '', text)  # Remove end tokens
        text = re.sub(r'<[^>]+>', '', text)  # Remove any remaining special tokens

        # Remove prompt fragments that might remain
        prompt_fragments = [
            "Extract all text from this document page, including headings, titles, and section names",
            "What are the headings and section titles in this document page?",
            "What is the text content of this document page?",
            "Extract all text from this document page",
            "What is the text content of this document?",
            "List all headings and titles in this document"
        ]

        for fragment in prompt_fragments:
            text = text.replace(fragment, "")

        # Remove common OCR artifacts and noise
        text = re.sub(r'\b[a-z]{1,2}\b', '', text)  # Remove single/double letter words
        text = re.sub(r'\d{4,}', '', text)  # Remove long numbers (likely noise)
        text = re.sub(r'[^\w\s\.\-\(\)]', ' ', text)  # Keep only basic punctuation

        text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
        text = text.strip()

        return text
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_name,
            "device": self.device,
            "cache_dir": self.cache_dir,
            "processor_vocab_size": len(self.processor.tokenizer) if self.processor else None,
            "model_parameters": sum(p.numel() for p in self.model.parameters()) if self.model else None,
            "model_loaded": self.model is not None,
            "processor_loaded": self.processor is not None
        }
